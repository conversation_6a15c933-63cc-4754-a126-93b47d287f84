<?php
include("config.php");

function AuthenticationLivreur($login,$password){
    global $conn;
    $passwordLivreur=md5("AdMiN189&#ç".md5($password));
    $stmt = $conn->prepare("SELECT * FROM livreur WHERE login_livreur=:login and password_livreur=:pwd");
    $stmt->bindParam(':login', $login);
    $stmt->bindParam(':pwd', $passwordLivreur);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count){
        return $data['id_livreur'];
    }else{
        return false;
    }
}

//Get Agence livreur by id
function GetAgenceLivreur($id){
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM livreur WHERE id_livreur=:id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count){
        return $data['id_agence'];
    }else{
        return false;
    }
}

//fix encoding
function fix_encode($str){
    if(mb_detect_encoding($str) !== 'UTF-8' or stripos($str, "§") === false) {
        $str = mb_convert_encoding($str,mb_detect_encoding($str),'UTF-8');
    }
    return $str;
}

//information de l'expediteur
function CodeInfoExpediteur($IdExpediteur){
    global $conn;
    $res_array = array();
    $stmt = $conn->prepare("SELECT * FROM expediteur WHERE id_exp=:id");
    $stmt->bindParam(':id', $IdExpediteur);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count>0){
        array_push($res_array,$data['nom_exp']);
        array_push($res_array,$data['tel_exp']);
        return $res_array;
    }else{
        return false;
    }
}

//id de l'expediteur
function CodeIDExpediteur($id_cmd){
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM commande WHERE id_cmd=:id");
    $stmt->bindParam(':id', $id_cmd);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count>0){
        return $data['id_exped'];
    }else{
        return false;
    }
}

function getInfoAgent_agence($id,$type){
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM agent WHERE id_agence=:id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count>0){
        return $data[$type];
    }else{
        return false;
    }
}

function getInfoAgence($id,$type){
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM agence WHERE id_agence=:id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count>0){
        return $data[$type];
    }else{
        return false;
    }
}

function getInfVendeur($id,$type){

    global $conn;
    $stmt = $conn->prepare("SELECT * FROM expediteur WHERE id_exp=:id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count>0){
        return $data[$type];
    }else{
        return false;
    }

}

function getInfoLivreur($id,$type){
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM livreur WHERE id_livreur=:id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count>0){
        return $data[$type];
    }else{
        return false;
    }
}

function get_nom_utlisateur_log($id_agent,$type_agent){
    switch ($type_agent) { //get_nom_utlisateur_log
        case 0:
            $nom_agent= getInfVendeur($id_agent,'nom_exp');
            break;
        case 1:
            $id_agence=getInfoAgent_agence($id_agent,'id_agence');
            $nom_agent= getInfoAgence($id_agence,'nom_agence').' ('. getInfoAgent_agence($id_agent,'nom_agent').')';
            break;
        case 2:
            $nom_agent= getInfoLivreur($id_agent,'nom_livreur');
            break;
    }
    return $nom_agent;
}

//Get name agence by id
function GetAgence($id){
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM agence WHERE id_agence=:id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count){
        return $data['nom_agence'];
    }else{
        return false;
    }
}

//Get Ville agence by id
function GetVille($id){
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM ville WHERE id_ville=:id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count){
        return $data['nom_ville'];
    }else{
        return false;
    }
}

//Get product name by id
function GetNomProduitCommande($id){
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM commande_ligne WHERE id_cmd=:id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $data_list_nom_produit =  "";
    $count = $stmt->rowCount();
    $i = 0;
    $retour = "";
    if($count){
        while($data =$stmt->fetch()){
            if(trim($data['nom_prod']) != "") {
                if ($i != 0) {
                    $retour = "\n";
                }
                $data_list_nom_produit = $data_list_nom_produit . $retour ."- ".trim($data['nom_prod']);
                $i++;
            }

        }
        return $data_list_nom_produit;
    }else{
        return false;
    }
}

//Get type cmd by id
function getTaillePieceCmd($id){
    $leger = 0;
    $moyen = 0;
    $grand = 0;
    $size = "";
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM commande_ligne WHERE id_cmd=:id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $count = $stmt->rowCount();
    if($count){
        while($data =$stmt->fetch()){
            switch ($data['taille']) {
                case "0" :
                    $leger++;
                    break;
                case "1" :
                    $moyen++;
                    break;
                case "2" :
                    $grand++;
                    break;
            }
        }
    }else{
        return false;
    }
    if($leger>0)
        $size .= $leger.' LEG | ';
    if($moyen>0)
        $size .= $moyen.' MOY | ';
    if($grand>0)
        $size .= $grand.' GRD';

    return rtrim($size, '| ');
}

//Get id commande by traking number
function GetIdCommande($tracking_number){
    global $conn;
    $stmt = $conn->prepare("SELECT * FROM commande WHERE code_barres_cmd = :code_barre");
    $stmt->bindParam(':code_barre', $tracking_number);
    $stmt->execute();
    $data =  $stmt->fetch();
    $count = $stmt->rowCount();
    if($count){
        return $data['id_cmd'];
    }else{
        return false;
    }
}

//create nouveau code runsheet
function getCodeRunsheet(){
    global $conn;
    $stmt = $conn->prepare("SELECT num_runsheet FROM runsheet ORDER BY id_runsheet DESC ");
    $stmt->execute();
    $data =  $stmt->fetch();
    $codeBase=$data['num_runsheet'];
    if($codeBase == ''){
        $code='00001';
    }else{
        $code = (int)$codeBase;
        $code++;
    }
    return sprintf("%05.2d", $code);
}

//generate code reglement
function getCodeReglement(){

    global $conn;
    $stmt = $conn->prepare("SELECT code FROM reglement_client_ligne ORDER BY id_reg DESC");
    $stmt->execute();
    $data =  $stmt->fetch();
    $codeBase=$data['code'];
    if($codeBase == ''){
        $code='00001';
    }else{
        $code = (int)$codeBase;
        $code++;
    }
    return sprintf("%05.2d", $code);
}


//Update status Shipment
function UpdateShipmentStatus($pickup){
    global $conn;
    $error_msg = array();






    //Pickup data
    $tracking_number = $pickup->tracking_number;
    $status = $pickup->etat;
    $frs = $pickup->login;
    $pwd = $pickup->pwd;
    $id_runsheet = $pickup->id_runsheet;
    $longitude = $pickup->longitude;
    $latitude = $pickup->latitude;
    $motif = $pickup->motif;
    $magasin = $pickup->magasin;
    $date_report = $pickup->date_report;
    $id_pickup = $pickup->id_pickup;
    $id_motif = $pickup->id_motif;
    $date_time = date('Y-m-d H:i:s');
    $date = date('Y-m-d H:i:s');
    $time_stamp_now = time();

    if($date_report == ''){
        $date_report =  date('Y-m-d');
    }







    // type_payement : 0=>espece ; 1=>cheque ; 2=>cheque_espece

    $montant_espece = $pickup->montant_espece;
    $type_payment = $pickup->type_payment;
    $list_mnt_cheque = $pickup->list_mnt_cheque;
    $list_num_cheque = $pickup->list_num_cheque;

    $nbr_colis_recu = $pickup->nbr_colis_recu;


   /*     $myfile = fopen("newfile.txt", "w") ;
        $txt = $status."\n".$motif."\n".$date_report;
        fwrite($myfile, $txt);
        fclose($myfile);*/


    if($id_runsheet!='') {  //si changement etat colis depuis la liste du runsheet
        //check if runsheet is not valid
        //fetch data
        $stmtValid = $conn->prepare("SELECT id_runsheet FROM runsheet WHERE id_runsheet=:id_runsheet and etat_runsheet = 0");
        $stmtValid->bindParam(':id_runsheet', $id_runsheet);
        $stmtValid->execute();
        $countValid = $stmtValid->rowCount();
    }

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }
    if(trim($tracking_number)==''){
        $error_msg[]= "The -tracking number- field is mandatory";
    }
    if(trim($status)==''){
        $error_msg[]= "The -status- field is mandatory";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1 and (  ($countValid>0 and $id_runsheet!='') or $id_runsheet=='' )){
        $id_cmd = GetIdCommande($tracking_number);
        $livreur = AuthenticationLivreur($frs,$pwd);
        $id_agence_livreur = GetAgenceLivreur($livreur);
        $codereglement = getCodeReglement() ;

        $where = "";
        if($id_runsheet==''){  //si changement etat colis depuis le pickup
            $where = " and etat_cmd = 0 ";
        }

        //fetch data
        $stmt = $conn->prepare("SELECT id_cmd , nbr_colis , ttc_cmd , id_agence_origine , id_exped FROM commande WHERE id_cmd=:id_cmd $where ORDER BY id_cmd DESC");
        $stmt->bindParam(':id_cmd', $id_cmd);
        $stmt->execute();
        $count = $stmt->rowCount();
        if($count<1){
            $error_msg[]= "Not found object";
        }else{

            $data = $stmt->fetch() ;
            $id_cmd = GetIdCommande($tracking_number);
            $date_time = date('Y-m-d H:i:s');
            $etat_cmd_array = array(0=>'En Attente',1=>'Ramassé',2=>'Au Dépot',3=>'En livraison',4=>'Reporté',5=>'Livré',6=>'Retour');

            $ttc_cmd = $data['ttc_cmd'] ;
			$status_log=$status;
            $stmt = $conn->prepare("UPDATE commande SET etat_cmd = :etat , etat_cmd_log_liv = :etat_log WHERE id_cmd = :id and (etat_cmd < 5 or etat_cmd=6) ");

            $stmt->bindParam(':etat', $status);
			$stmt->bindParam(':etat_log', $status_log);
            $stmt->bindParam(':id', $id_cmd);
            $stmt->execute();

            if(($status == 5) || ($status == 6)){
                $stmt = $conn->prepare("UPDATE commande SET date_livraison_retour = :date_livraison_retour WHERE id_cmd = :id");
                $stmt->bindParam(':date_livraison_retour', time());
                $stmt->bindParam(':id', $id_cmd);
                $stmt->execute();
            }

            if($status == 4){
                $stmt = $conn->prepare("UPDATE commande SET date_cmd_reporte = :date_cmd_reporte WHERE id_cmd = :id and etat_cmd < 5 ");
                $stmt->bindParam(':date_cmd_reporte', time());
                $stmt->bindParam(':id', $id_cmd);
                $stmt->execute();
            }


            if($id_runsheet==''){  //si changement etat colis depuis le pickup
                $stmt = $conn->prepare("UPDATE commande SET id_pickup = :id_pickup , nbr_colis_ram = :nbr_colis_ram WHERE id_cmd = :id");
                $stmt->bindParam(':id_pickup', $id_pickup);
                $stmt->bindParam(':nbr_colis_ram', $nbr_colis_recu);
                $stmt->bindParam(':id', $id_cmd);
                $stmt->execute();
            }else{
                //update etat runsheet log 156156165156165
                //Insert state
                $stmt = $conn->prepare("UPDATE runsheet_log SET date_log_runsheet = :date_log_runsheet ,etat_cmd_liv = :etat_cmd_liv WHERE id_cmd = :id_cmd and id_run = :id_runsheet");
                $stmt->bindParam(':id_cmd', $id_cmd);
                $stmt->bindParam(':id_runsheet', $id_runsheet);
                $stmt->bindParam(':date_log_runsheet', time());
                $stmt->bindParam(':etat_cmd_liv', $status );
                $stmt->execute();

                if($status == 5) {

                    // delete all reglement de commande
                    //fetch data
                    $stmt = $conn->prepare("SELECT id_reg FROM reglement_client_ligne WHERE id_cmd=:id_cmd ");
                    $stmt->bindParam(':id_cmd', $id_cmd);
                    $stmt->execute();
                    $countreg = $stmt->rowCount();
                    if($countreg > 0) {
                        $datareg = $stmt->fetch() ;
                        $id_reg = $datareg['id_reg'] ;

                        $stmt = $conn->prepare("DELETE FROM reglement_client_cheque WHERE id_reg = :id_reg");
                        $stmt->bindValue(':id_reg', $id_reg);
                        $stmt->execute();

                        $stmt = $conn->prepare("DELETE FROM reglement_client_ligne WHERE id_cmd = :id_cmd");
                        $stmt->bindValue(':id_cmd', $id_cmd);
                        $stmt->execute();

                    }


                    //insertion payment
                    // type_payement : 0=>espece ; 1=>cheque ; 2=>cheque_espece
                    if ($type_payment == 0) { // espece
                        $mnt_ecart=$ttc_cmd-$montant_espece;
                        $stmt = $conn->prepare("INSERT INTO reglement_client_ligne ( id_cmd, mnt_cmd, mnt_paye, mnt_ecart, mnt_epece, type_reg, id_util, type_util , `date` , id_agence_origine ,id_agence ,id_exped ,code) VALUES (:id_cmd,:mnt_cmd,:mnt_paye,:mnt_ecart,:mnt_epece,:type_reg,:id_util,:type_util , :time_stamp_now,:id_agence_origine , :id_agence , :id_exped,:code)");
                        $stmt->bindValue(':id_cmd', $id_cmd);
                        $stmt->bindValue(':mnt_cmd', $ttc_cmd);
                        $stmt->bindValue(':mnt_paye', $montant_espece);
                        $stmt->bindValue(':mnt_epece', $montant_espece);
                        $stmt->bindValue(':mnt_ecart', $mnt_ecart);
                        $stmt->bindValue(':type_reg', 2);
                        $stmt->bindValue(':id_util', $livreur);
                        $stmt->bindValue(':type_util', 1);
                        $stmt->bindValue(':time_stamp_now', $time_stamp_now);
                        $stmt->bindValue(':id_agence_origine', $data['id_agence_origine']);
                        $stmt->bindValue(':id_agence', $id_agence_livreur);
                        $stmt->bindValue(':id_exped', $data['id_exped']);
                        $stmt->bindValue(':code', $codereglement);
                        $stmt->execute();

                        $id_reglement = $conn->lastInsertId();

                        if($montant_espece == 0){
                            $etat_paye = 0 ;
                        }else if($montant_espece >= $ttc_cmd){
                            $etat_paye = 1 ;
                        }else{
                            $etat_paye = 2 ;
                        }

                        // insert code a barre reglement
                        $codeReg='5112'.str_pad($data['id_exped'], 4, '0', STR_PAD_LEFT).str_pad($id_reglement, 5, '0', STR_PAD_LEFT);
                        $stmt_reg = $conn->prepare("update reglement_client_ligne set code= :codeReg WHERE id_reg = :id_reglement");
                        $stmt_reg->bindValue(':codeReg', $codeReg);
                        $stmt_reg->bindValue(':id_reglement', $id_reglement);
                        $stmt_reg->execute();


                        /// update in table commande
                        $stmt = $conn->prepare("UPDATE commande SET etat_paye = :etat_paye ,type_reg_cmd = 2 WHERE id_cmd = :id");
                        $stmt->bindParam(':etat_paye', $etat_paye);
                        $stmt->bindParam(':id', $id_cmd);
                        $stmt->execute();



                    } else if ($type_payment == 1) { // cheque
                        
                        $sum_cheque = 0;
                        $array_mnt_cheque = explode(',', $list_mnt_cheque);
                        $array_num_cheque = explode(',', $list_num_cheque);
                        $sum_cheque = array_sum($array_mnt_cheque);
                        $montant_total = $sum_cheque;
                        $mnt_ecart=$ttc_cmd-$montant_total;

                        $stmt = $conn->prepare("INSERT INTO reglement_client_ligne ( id_cmd, mnt_cmd, mnt_paye, mnt_epece, mnt_cheque, mnt_ecart, observation_reg, type_reg, id_util, type_util , `date` , id_agence_origine ,id_agence ,id_exped ,code) VALUES (:id_cmd,:mnt_cmd,:mnt_paye,:mnt_epece,:mnt_cheque,:mnt_ecart,:observation_reg,:type_reg,:id_util,:type_util, :time_stamp_now,:id_agence_origine , :id_agence , :id_exped,:code)");
                        $stmt->bindValue(':id_cmd', $id_cmd);
                        $stmt->bindValue(':mnt_cmd', $ttc_cmd);
                        $stmt->bindValue(':mnt_paye', $montant_total);
                        $stmt->bindValue(':mnt_ecart', $mnt_ecart);
                        $stmt->bindValue(':mnt_epece', 0);
                        $stmt->bindValue(':mnt_cheque', $sum_cheque);
                        $stmt->bindValue(':observation_reg', "");
                        $stmt->bindValue(':type_reg', 1);
                        $stmt->bindValue(':id_util', $livreur);
                        $stmt->bindValue(':type_util', 1);
                        $stmt->bindValue(':time_stamp_now', $time_stamp_now);
                        $stmt->bindValue(':id_agence_origine', $data['id_agence_origine']);
                        $stmt->bindValue(':id_agence', $id_agence_livreur);
                        $stmt->bindValue(':id_exped', $data['id_exped']);
                        $stmt->bindValue(':code', $codereglement);
                        $stmt->execute();
                        $id_reglement = $conn->lastInsertId();


                        // insert code a barre reglement
                        $codeReg='5112'.str_pad($data['id_exped'], 4, '0', STR_PAD_LEFT).str_pad($id_reglement, 5, '0', STR_PAD_LEFT);
                        $stmt_reg = $conn->prepare("update reglement_client_ligne set code= :codeReg WHERE id_reg = :id_reglement");
                        $stmt_reg->bindValue(':codeReg', $codeReg);
                        $stmt_reg->bindValue(':id_reglement', $id_reglement);
                        $stmt_reg->execute();


                        for ($i = 0; $i < count($array_mnt_cheque); $i++) {
                            if (!($array_num_cheque[$i] == '') && !($array_mnt_cheque[$i] == '')) {
                                $stmt = $conn->prepare("INSERT INTO reglement_client_cheque (id_reg,mnt_cheque,num_cheque) VALUES (:id_reg,:mnt_cheque,:num_cheque)");
                                $stmt->bindValue(':id_reg', $id_reglement);
                                $stmt->bindValue(':mnt_cheque', $array_mnt_cheque[$i]);
                                $stmt->bindValue(':num_cheque', $array_num_cheque[$i]);
                                $stmt->execute();
                            }
                        }


                        if($montant_total == 0){
                            $etat_paye = 0 ;
                        }else if($montant_total >= $ttc_cmd){
                            $etat_paye = 1 ;
                        }else{
                            $etat_paye = 2 ;
                        }
                        /// update in table commande
                        $stmt = $conn->prepare("UPDATE commande SET etat_paye = :etat_paye ,type_reg_cmd = 1 WHERE id_cmd = :id");
                        $stmt->bindParam(':etat_paye', $etat_paye);
                        $stmt->bindParam(':id', $id_cmd);
                        $stmt->execute();

                    } else if ($type_payment == 2) { // espece and cheque

                        $array_mnt_cheque = explode(',', $list_mnt_cheque);
                        $array_num_cheque = explode(',', $list_num_cheque);
                        $sum_cheque = array_sum($array_mnt_cheque);
                        $montant_total = $sum_cheque + $montant_espece;
                        $mnt_ecart=$ttc_cmd-$montant_total;


                        $stmt = $conn->prepare("INSERT INTO reglement_client_ligne ( id_cmd, mnt_cmd, mnt_paye, mnt_epece, mnt_cheque, mnt_ecart, observation_reg, type_reg, id_util, type_util , `date` , id_agence_origine ,id_agence ,id_exped ,code) VALUES (:id_cmd,:mnt_cmd,:mnt_paye,:mnt_epece,:mnt_cheque,:mnt_ecart,:observation_reg,:type_reg,:id_util,:type_util, :time_stamp_now,:id_agence_origine , :id_agence , :id_exped,:code)");
                        $stmt->bindValue(':id_cmd', $id_cmd);
                        $stmt->bindValue(':mnt_cmd', $ttc_cmd);
                        $stmt->bindValue(':mnt_paye', $montant_total);
                        $stmt->bindValue(':mnt_ecart', $mnt_ecart);
                        $stmt->bindValue(':mnt_epece', $montant_espece);
                        $stmt->bindValue(':mnt_cheque', $sum_cheque);
                        $stmt->bindValue(':observation_reg', "");
                        $stmt->bindValue(':type_reg', 3);
                        $stmt->bindValue(':id_util', $livreur);
                        $stmt->bindValue(':type_util', 1);
                        $stmt->bindValue(':time_stamp_now', $time_stamp_now);
                        $stmt->bindValue(':id_agence_origine', $data['id_agence_origine']);
                        $stmt->bindValue(':id_agence', $id_agence_livreur);
                        $stmt->bindValue(':id_exped', $data['id_exped']);
                        $stmt->bindValue(':code', $codereglement);
                        $stmt->execute();

                        $id_reglement = $conn->lastInsertId();

                        // insert code a barre reglement
                        $codeReg='5112'.str_pad($data['id_exped'], 4, '0', STR_PAD_LEFT).str_pad($id_reglement, 5, '0', STR_PAD_LEFT);
                        $stmt_reg = $conn->prepare("update reglement_client_ligne set code= :codeReg WHERE id_reg = :id_reglement");
                        $stmt_reg->bindValue(':codeReg', $codeReg);
                        $stmt_reg->bindValue(':id_reglement', $id_reglement);
                        $stmt_reg->execute();

                        for ($i = 0; $i < count($array_mnt_cheque); $i++) {
                            if (!($array_num_cheque[$i] == '') && !($array_mnt_cheque[$i] == '')) {
                                $stmt = $conn->prepare("INSERT INTO reglement_client_cheque (id_reg,mnt_cheque,num_cheque) VALUES (:id_reg,:mnt_cheque,:num_cheque)");
                                $stmt->bindValue(':id_reg', $id_reglement);
                                $stmt->bindValue(':mnt_cheque', $array_mnt_cheque[$i]);
                                $stmt->bindValue(':num_cheque', $array_num_cheque[$i]);
                                $stmt->execute();
                            }
                        }


                        if($montant_total == 0){
                            $etat_paye = 0 ;
                        }else if($montant_total >= $ttc_cmd){
                            $etat_paye = 1 ;
                        }else{
                            $etat_paye = 2 ;
                        }
                        /// update in table commande
                        $stmt = $conn->prepare("UPDATE commande SET etat_paye = :etat_paye ,type_reg_cmd = 3 WHERE id_cmd = :id");
                        $stmt->bindParam(':etat_paye', $etat_paye);
                        $stmt->bindParam(':id', $id_cmd);
                        $stmt->execute();

                    }
                }


            }

            if($id_motif != '0'){
                $id_observation = intval(trim($id_motif));
                $text_motif = "";
                $stmt_observation = $conn->prepare("SELECT * FROM observation WHERE id = :id_observation");
                $stmt_observation->bindParam(':id_observation',$id_observation);
                $stmt_observation->execute();
                $data_observation =  $stmt_observation->fetch();
                $text_motif = $data_observation['observation'] ;

                if($id_observation == 1 or $id_observation == 2){
                    $motif_all = $etat_cmd_array[$status]."| ".$motif." | ".$date_report;
                }else{
                    $motif_all = $etat_cmd_array[$status]."| ".$text_motif." | ".$date_report;
                }


            }else{
                $id_observation = 0 ;
                $motif_all = $etat_cmd_array[$status]." | ".$date_report;
            }



            //Insert state
            $stmt = $conn->prepare("INSERT INTO commande_log (id_cmd ,id_agent ,action_log ,type_agent ,date_log , observation ,longitude,latitude,id_observation,id_agence) VALUES (:id_cmd,:id_agent,:etat,2,:date_log,:observation,:longitude,:latitude,:id_observation,:id_agence)");
            $stmt->bindParam(':id_cmd', $id_cmd);
            $stmt->bindParam(':etat', $status);
            $stmt->bindParam(':id_agent', $livreur);
            $stmt->bindParam(':id_agence', $id_agence_livreur);
            $stmt->bindParam(':date_log', strtotime($date_time));
            $stmt->bindParam(':observation', $motif_all );
            $stmt->bindParam(':longitude', $longitude );
            $stmt->bindParam(':latitude', $latitude );
            $stmt->bindParam(':id_observation', $id_observation );
            $stmt->execute();
        }
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->tracking_number = $tracking_number;
        $PickupReturn->status = $status;
        $PickupReturn->message = $etat_cmd_array[$status];
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->tracking_number = $tracking_number;
        $PickupReturn->status = '';
        $PickupReturn->message = '';

        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//Get all active Runsheet
function ListRunsheet($livreur){
    global $conn;
    $error_msg = array();

    //livreur data
    $frs = $livreur->login;
    $pwd = $livreur->pwd;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){

        $livreur = AuthenticationLivreur($frs,$pwd);
        //fetch runsheet data
        $stmt = $conn->prepare("select * from runsheet where id_liv_runsheet=:livreur and etat_runsheet=0 and nbr_cmd_runsheet <> 0 order by id_runsheet desc");
        $stmt->bindParam(':livreur', $livreur);
        $stmt->execute();

        while($data = $stmt->fetch()) {

            //nbr colis
            $stmt_total_colis = $conn->prepare("SELECT COUNT(id_log) as total_colis FROM runsheet_log WHERE id_run=:id_runsheet");
            $stmt_total_colis->bindParam(':id_runsheet', $data['id_runsheet']);
            $stmt_total_colis->execute();
            $data_total_colis =  $stmt_total_colis->fetch();
            //nbr colis livré
            $stmt_total_colis_livre = $conn->prepare("SELECT COUNT(id_log) as total_colis FROM runsheet_log WHERE id_run=:id_runsheet and etat_cmd_liv=2");
            $stmt_total_colis_livre->bindParam(':id_runsheet', $data['id_runsheet']);
            $stmt_total_colis_livre->execute();
            $data_total_colis_livre =  $stmt_total_colis_livre->fetch();

            $sate_etat = new stdClass();
            $sate_etat->id = $data['id_runsheet'];
            $sate_etat->date = $data['date_runsheet'];
            $sate_etat->colis = $data_total_colis['total_colis'];
            $sate_etat->colis_livre = $data_total_colis_livre['total_colis'];
            $commande[] = $sate_etat;
        }

        $count = $stmt->rowCount();
        if($count<1){
            $error_msg[]= "Not found object";
        }
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->commande = $commande;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->status = '';
        $PickupReturn->commande = '';
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//Get all active parcel of runsheet
function ListParcelRunsheet($livreur){
    global $conn;
    $error_msg = array();

    //livreur data
    $frs = $livreur->login;
    $pwd = $livreur->pwd;
    $runsheet = $livreur->runsheet;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){

        $livreur_id = AuthenticationLivreur($frs,$pwd);
        //fetch runsheet data
        $stmt = $conn->prepare("select * from runsheet_log where  id_run=:id_runsheet order by id_log asc");
        $stmt->bindParam(':id_runsheet', $runsheet);
        $stmt->execute();

        while($data = $stmt->fetch()) {

            //info commande
            $stmt_info_colis = $conn->prepare("SELECT * FROM commande WHERE id_cmd=:id");
            $stmt_info_colis->bindParam(':id', $data['id_cmd']);
            $stmt_info_colis->execute();
            $data_info_colis =  $stmt_info_colis->fetch();

            $fournisseur = CodeInfoExpediteur($data_info_colis['id_exped']);
            $sate_etat = new stdClass();


            $stmt_zone = $conn->prepare("SELECT * FROM livreur_zone WHERE id=:id");
            $stmt_zone->bindParam(':id',$data_info_colis['id_zone_liv']);
            $stmt_zone->execute();


            $data_zone = $stmt_zone->fetch();// fetch les zones du livreur
            $zone_class = new stdClass();
            $zone_class->name = $data_zone['zone_name'];






            $sate_etat->code_barre = $data_info_colis['code_barres_cmd'];
            $sate_etat->fournisseur = fix_encode($fournisseur[0]);
            $sate_etat->fournisseur_tel = str_replace(array(" ", "."), '', $fournisseur[1]);
            $sate_etat->fournisseur_tel2 = "";
            $sate_etat->designation = fix_encode($data_info_colis['contenu_cmd']) ; //fix_encode($data_info_colis['designation']);
            $sate_etat->montant = $data_info_colis['ttc_cmd'];
            $sate_etat->client = fix_encode($data_info_colis['nom_cli']);
            $sate_etat->client_adresse= fix_encode(trim($data_info_colis['adr_cli']));
            $sate_etat->client_tel = str_replace(array(" ", "."), '', $data_info_colis['tel_cli']);
            $sate_etat->client_tel2 = str_replace(array(" ", "."), '', $data_info_colis['tel_cli2']);
            $sate_etat->gouvernerat = "" ;
            $sate_etat->ville = GetVille($data_info_colis['ville_cli']);
            $sate_etat->etat = $data_info_colis['etat_cmd'];
            $sate_etat->msg = $data_info_colis['observ_cmd'];
            $sate_etat->nbr_colis = $data_info_colis['nbr_colis'];
            $sate_etat->nbr_tentative = $data_info_colis['nbr_tentative'];
            $sate_etat->echange_cmd = $data_info_colis['echange_cmd'];
            $sate_etat->id_zone = $data_info_colis['id_zone_liv'];
            $sate_etat->name = $zone_class->name;
            if($data_info_colis['echange_cmd'] == 1){
                //info commande echange
                $stmt_info_colis_echange = $conn->prepare("SELECT * FROM produit_echange WHERE id_cmd=:id");
                $stmt_info_colis_echange->bindParam(':id', $data['id_cmd']);
                $stmt_info_colis_echange->execute();
                $data_info_colis_echange =  $stmt_info_colis_echange->fetch();
                $sate_etat->nom_produit_echange = $data_info_colis_echange['nom_produit'];
            }else{
                $sate_etat->nom_produit_echange = "";
            }
            $commande[] = $sate_etat;
        }

        $count = $stmt->rowCount();
        if($count<1){
            $error_msg[]= "Not found object";
        }
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->commande = $commande;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->status = '';
        $PickupReturn->commande = '';
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//Update status Shipment
function LogCall($call){
    global $conn;
    $error_msg = array();

    //call data
    $code_barre = $call->code_barre;
    $id_runsheet = $call->id_runsheet;
    $expediteur = $call->expediteur;
    $file_name = $call->file_name;
    $date_call = $call->date_call;
    $frs = $call->login;
    $pwd = $call->pwd;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }
    if(trim($id_runsheet)==''){
        $error_msg[]= "The -id_runsheet- field is mandatory";
    }
    if(trim($code_barre)==''){
        $error_msg[]= "The -code_barre- field is mandatory";
    }
    if(trim($expediteur)==''){
        $error_msg[]= "The -expediteur- field is mandatory";
    }
    if(trim($file_name)==''){
        $error_msg[]= "The -file_name- field is mandatory";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        $id_cmd = GetIdCommande($code_barre);
        //Insert log
        $stmt = $conn->prepare("INSERT INTO log_call (id_cmd,expediteur,file_name,date_call,id_runsheet) VALUES (:id_cmd,:expediteur,:file_name,:date_call,:id_runsheet)");
        $stmt->bindParam(':id_cmd', $id_cmd);
        $stmt->bindParam(':expediteur', $expediteur);
        $stmt->bindParam(':file_name', $file_name);
        $stmt->bindParam(':date_call', $date_call);
        $stmt->bindParam(':id_runsheet', $id_runsheet);
        $stmt->execute();
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->etat = 1;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->etat = 0;
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//Get all active pickup (today)
function ListPickUp($livreur){
    global $conn;
    $error_msg = array();

    //livreur data
    $frs = $livreur->login;
    $pwd = $livreur->pwd;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){

        $livreur = AuthenticationLivreur($frs,$pwd);
        //fetch runsheet data
        $stmt = $conn->prepare("select * from `ramassage` where `id_livreur` = :id_livreur and date_ram = CAST( NOW() AS Date ) order by id_ram desc");
        $stmt->bindParam(':id_livreur', $livreur);
        $stmt->execute();

        while($data = $stmt->fetch()) {

            //info expediteur
            $stmt_frs = $conn->prepare("SELECT * FROM expediteur WHERE id_exp=:id_fournisseur");
            $stmt_frs->bindParam(':id_fournisseur', $data['id_exped']);
            $stmt_frs->execute();
            $data_frs =  $stmt_frs->fetch();
            //nbr colis
            $stmt_total_colis = $conn->prepare("SELECT COUNT(id_cmd) as total_colis FROM commande WHERE id_exped=:id_frs and etat_cmd=0");
            $stmt_total_colis->bindParam(':id_frs', $data['id_exped']);
            $stmt_total_colis->execute();
            $data_total_colis =  $stmt_total_colis->fetch();
            //adress pickup
            $stmt_adress = $conn->prepare("SELECT * FROM adresse WHERE id=:id_adress ");
            $stmt_adress->bindParam(':id_adress', $data['adresse_ram']);
            $stmt_adress->execute();
            $data_adress =  $stmt_adress->fetch();

            $tel_frs = $data_frs['tel_exp'];

            $sate_etat = new stdClass();
            $sate_etat->id = $data['id_ram'];
            $sate_etat->fournisseur = $data_frs['nom_exp'];
            $sate_etat->fournisseur_tel = $tel_frs;
            $sate_etat->fournisseur_tel2 = "";
            $sate_etat->adresse = $data['adresse_ram'];
            $sate_etat->start_pickup = $data['start_ram'];
            $sate_etat->end_pickup = $data['end_ram'];
            $sate_etat->etat_pickup = $data['etat_ram'];
            $sate_etat->commentaire_pickup = $data['observ_ram'];
            $sate_etat->nbr_colis = $data_total_colis['total_colis'];
            $commande[] = $sate_etat;
        }

        $count = $stmt->rowCount();
        if($count<1){
            $error_msg[]= "Not found object";
        }
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->commande = $commande;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->status = '';
        $PickupReturn->commande = '';
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//Update status Shipment
function UpdatePickupStatus($pickup){
    global $conn;
    $error_msg = array();

    //Pickup data
    $frs = $pickup->login;
    $pwd = $pickup->pwd;
    $status = $pickup->etat_pickup;
    $id = $pickup->id_pickup;
    $nbr_colis = $pickup->nbr_colis;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }
    if(trim($id)==''){
        $error_msg[]= "The -id- field is mandatory";
    }
    if(trim($status)==''){
        $error_msg[]= "The -etat- field is mandatory";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        $stmtPickup = $conn->prepare("SELECT * FROM ramassage WHERE id_ram = :id  ");
        $stmtPickup->bindParam(':id', $id);
        $stmtPickup->execute();
        $excTypeRam =  $stmtPickup->fetch();

        if($excTypeRam['type_ram']==2){//cree par livreur

            $stmt_info_colis = $conn->prepare("SELECT * FROM commande WHERE id_pickup = :id");
            $stmt_info_colis->bindParam(':id', $id);
            $stmt_info_colis->execute();
            $data_info_colis =  $stmt_info_colis->fetch();
            $count = $stmt_info_colis->rowCount();
            if($count<1){
                $error_msg[]= "Not found object";
            }else{
                $end_ram=date('H');
                $adresse_ram = getInfVendeur($data_info_colis['id_exped'],'adr_exp');
                $num_resp = getInfVendeur($data_info_colis['id_exped'],'tel_exp');
                $id_exped = $data_info_colis['id_exped'];
                $stmt = $conn->prepare("UPDATE ramassage SET  etat_ram = :etat_pickup, nbr_colis = :nbr_colis, adresse_ram = :adresse_ram, num_resp = :num_resp, id_exped = :id_exped,  end_ram = :end_ram WHERE id_ram = :id");
                $stmt->bindParam(':etat_pickup', $status);
                $stmt->bindParam(':id', $id);
                $stmt->bindParam(':nbr_colis', $nbr_colis);
                $stmt->bindParam(':adresse_ram', $adresse_ram);
                $stmt->bindParam(':num_resp', $num_resp);
                $stmt->bindParam(':id_exped', $id_exped);
                $stmt->bindParam(':end_ram', $end_ram);
                $stmt->execute();

            }

        }else{
            $stmt = $conn->prepare("UPDATE ramassage SET etat_ram = :etat_pickup , nbr_colis = :nbr_colis  WHERE id_ram = :id");
            $stmt->bindParam(':etat_pickup', $status);
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':nbr_colis', $nbr_colis);
            $stmt->execute();
        }
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->id = $id;
        $PickupReturn->status = $status;

        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->id = $id;
        $PickupReturn->status = $status;

        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//Get all states Shipment
function TrackShipment($pickup){
    global $conn;
    $error_msg = array();

    //Pickup data
    $tracking_number = $pickup->tracking_number;
    $frs = $pickup->login;
    $pwd = $pickup->pwd;
    $etat_array = array(0=>'En attente',1=>'En cours',2=>'Livrée',11=>'RTN dépot', 7=>'Retour Client / Agence',8=>'Au dépôt',10=>'Livrées payées',30=>'Retour réçu',32=>'Retour payé',31=>'Retour définitif',5=>'Retour Expéditeur');
    //0=attente 1=rammassé 2=auDépot 3=Enlivraison 4=reporté  5=livré 6=retour
    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }
    if(trim($tracking_number)==''){
        $error_msg[]= "The -tracking number- field is mandatory";
    }

    $id_cmd = GetIdCommande($tracking_number);

    if($id_cmd == 0){
        $error_msg[]= "The -tracking number- is wrong";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1 ){
        //fetch data
        $stmt = $conn->prepare("SELECT * FROM commande_log WHERE id_cmd=:id_cmd ORDER BY date_log ASC ");
        $stmt->bindParam(':id_cmd', $id_cmd);
        $stmt->execute();

        while($data = $stmt->fetch()) {
            $id_runsheet = $data['id_runsheet'] ;
            $num_run = '' ;

            if( $id_runsheet != 0 ) {
                $stmtrun = $conn->prepare("SELECT * FROM runsheet WHERE id_runsheet = :id_runsheet ");
                $stmtrun->bindParam(':id_runsheet', $id_runsheet );
                $stmtrun->execute();
                $count = $stmtrun->rowCount();
                if($count != 0 ) {
                    $datarun = $stmtrun->fetch();
                    $num_run = $datarun['num_runsheet'];
                }
            }

            $sate_etat = new stdClass();
            $type_action=$data['type_action'];
            $action_log=$data['action_log'];
            $id_agent=$data['id_agent'];
            $id_agence_inter=$data['id_agence'];
            $id_agence_inter_dest=$data['id_agence_dest'];
            $type_agent=$data['type_agent'];
            $date_log=date('d-m-Y',$data['date_log']);
            $heure_log=date('H:i',$data['date_log']);
            $type_agent=$data['type_agent'];
            $nom_agent=get_nom_utlisateur_log($id_agent,$type_agent);
            $sate_etat->dt = date('d-m-Y H:i',$data['date_log']);
            if($type_action=='0'){ //log_etat_commande
                switch ($action_log) {
                    case 0:
                        $description= "Créé par ".$nom_agent.'';
                        $sate_etat->etat = 0;
                        $sate_etat->message = $description ;
                        break;
                    case 1:
                        $description= "Ramassé par ".$nom_agent.' vers '.GetAgence($id_agence_inter).'';
                        $sate_etat->etat = 1;
                        $sate_etat->message = $description ;
                        break;
                    case 2:
                        $description= "Reçu par ".$nom_agent.'' ;
                        $sate_etat->etat = 2;
                        $sate_etat->message = $description ;
                        break;
                    case 3:
                        $description= "sortie du ".$nom_agent.'';
                        $sate_etat->etat = 3;
                        $sate_etat->message = $description ;
                        break;
                    case 4:
                        $description= "Reporté par ".$nom_agent.'';
                        $sate_etat->etat = 4;
                        $sate_etat->message = $description ;
                        break;
                    case 5:
                        $description= "Livré par ".$nom_agent.'';
                        $sate_etat->etat = 5;
                        $sate_etat->message = $description ;
                        break;
                    case 6:
                        $description= "Retour par ".$nom_agent.'';
                        $sate_etat->etat = 6;
                        $sate_etat->message = $description ;
                        break;
                    case 7:
                        $description= "Retirer du runsheet ".$num_run." Par".$nom_agent.'';
                        $sate_etat->etat = 7;
                        $sate_etat->message = $description ;
                        break;
                }
            }else if($type_action=='1'){ //log_etat_retour
                switch ($action_log) {
                    case 0:
                        $description= "Retour au dépôt par ".$nom_agent;
                        $sate_etat->etat = 4;
                        $sate_etat->message = $description ;
                        break;
                    case 1:
                        $description= "Reçue par ".$nom_agent;
                        $sate_etat->etat = 2;
                        $sate_etat->message = $description ;
                        break;
                    case 2:
                        $fournisseur = CodeInfoExpediteur(CodeIDExpediteur($data['id_cmd']));
                        $description= "Reçue par ".$fournisseur[0] ;
                        $sate_etat->etat = 5;
                        $sate_etat->message = $description ;
                        break;
                }
            }else if($type_action=='2'){ //log_etat_paiment
                switch ($action_log) {
                    case 1:
                        $description= "Paiement";
                        $sate_etat->etat = 8;
                        $sate_etat->message = $description ;
                        break;
                }
            }else if($type_action=='3'){ //inetr_depot
                switch ($action_log) {
                    case 0:
                        $description = "Transmit de " . getInfoAgence($id_agence_inter, 'nom_agence') . ' à ' . getInfoAgence($id_agence_inter_dest, 'nom_agence') . '.';
                        $sate_etat->etat = 9;
                        $sate_etat->message = $description;
                        break;
                    case 1:
                        $description = "Accepté par " . getInfoAgence($id_agence_inter_dest, 'nom_agence') . ' envoyé par ' . getInfoAgence($id_agence_inter, 'nom_agence') . '.';
                        $sate_etat->etat = 10;
                        $sate_etat->message = $description;
                        break;
                }
            }
            $etat[] = $sate_etat;
        }

        $count = $stmt->rowCount();
        if($count<1){
            $error_msg[]= "Not found object";
            $error_msg[]= $id_cmd;
        }
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->tracking_number = $tracking_number;
        $PickupReturn->status = $etat;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->tracking_number = $tracking_number;
        $PickupReturn->status = '';

        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//Get all info livreur
function Authentication($livreur)
{
    global $conn;
    $error_msg = array();

    //livreur data
    $frs = $livreur->login;
    $pwd = $livreur->pwd;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){

        $livreur = AuthenticationLivreur($frs,$pwd);
        //fetch runsheet data
        //info expediteur
        $stmt_liv = $conn->prepare("SELECT * FROM livreur WHERE id_livreur=:id");
        $stmt_liv->bindParam(':id',$livreur);
        $stmt_liv->execute();
        $data_liv =  $stmt_liv->fetch();


        $count = $stmt_liv->rowCount();
        if($count<1){
            $error_msg[]= "Not found object";
        }
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){


        $stmt_zone = $conn->prepare("SELECT * FROM livreur_zone WHERE id_liv=:id");
        $stmt_zone->bindParam(':id',$livreur);
        $stmt_zone->execute();
        //$data_zone=  $stmt_zone->fetch();
        $array_zone= array();
        $count_zone = $stmt_zone->rowCount();
        if($count_zone<1){
            $error_msg[]= "Not found object";
        }

        while($data_zone = $stmt_zone->fetch()) { // fetch les zones du livreur
            $zone_class = new stdClass();
            $zone_class->id_zone = $data_zone['id'];
            $zone_class->name = $data_zone['zone_name'];
            $array_zone[] = $zone_class;
        }


        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->id = trim($data_liv['id_livreur']);
        $PickupReturn->nom = trim($data_liv['nom_livreur']);
        $PickupReturn->tel = trim($data_liv['tel_livreur']);
        $PickupReturn->matricule = trim($data_liv['matricule_livreur']);
        $PickupReturn->cin = trim($data_liv['date_inscrit']);
        $PickupReturn->id_agence = trim($data_liv['id_agence']);
        $PickupReturn->nom_agence = GetAgence($data_liv['id_agence']);
        $PickupReturn->array_zone = $array_zone;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->status = "";
        $PickupReturn->id = "";
        $PickupReturn->nom = "";
        $PickupReturn->tel = "";
        $PickupReturn->matricule = "";
        $PickupReturn->cin = "";
        $PickupReturn->id_agence = "";
        $PickupReturn->nom_agence = "";
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//Get all sms
function GetSms($livreur){
    global $conn;
    $error_msg = array();

    //livreur data
    $frs = $livreur->login;
    $pwd = $livreur->pwd;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){

        $livreur = AuthenticationLivreur($frs,$pwd);
        //fetch runsheet data
        //info expediteur
        $stmt_sms = $conn->prepare("SELECT * FROM sms WHERE id =1");
        $stmt_sms->execute();
        $data_sms =  $stmt_sms->fetch();


        $count = $stmt_sms->rowCount();
        if($count<1){
            $error_msg[]= "Not found object";
        }
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->tous_sms = fix_encode($data_sms['tous_sms']);
        $PickupReturn->injoignable = fix_encode($data_sms['injoignable_sms']);
        $PickupReturn->expediteur = fix_encode($data_sms['expediteur_sms']);
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->tous_sms = "";
        $PickupReturn->injoignable = "";
        $PickupReturn->expediteur = "";
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//Get all observation
function GetObservation($livreur){
    global $conn;
    $error_msg = array();

    //livreur data
    $frs = $livreur->login;
    $pwd = $livreur->pwd;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){

        $livreur = AuthenticationLivreur($frs,$pwd);
        $stmt_observation = $conn->prepare("SELECT * FROM observation ");
        $stmt_observation->execute();

        while($data = $stmt_observation->fetch()) {

            $sate_observation = new stdClass();

            $sate_observation->id = $data['id'];
            $sate_observation->type = $data['type'];
            $sate_observation->observation = $data['observation'];
            $observation[] = $sate_observation;
        }

        $count = $stmt_observation->rowCount();
        if($count<1){
            $error_msg[]= "Not found object";
        }
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->observation = $observation;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->observation = "";
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}


//Get info parcel
function InfoParcel($pickup){
    global $conn;
    $error_msg = array();

    //pickup data
    $tracking_number = $pickup->tracking_number;
    $frs = $pickup->login;
    $pwd = $pickup->pwd;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }

    if(trim($tracking_number)==''){
        $error_msg[]= "The -tracking number- field is mandatory";
    }

    $type_cmd_array = array(0=>'Légére',1=>'Moyenne',2=>'Grande');
    $etat_cmd_array = array(0=>'En Attente',1=>'Ramassé',2=>'Au Dépot',3=>'En livraison',4=>'Reporté',5=>'Livré',6=>'Retour');

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        //info commande
        $stmt_info_colis = $conn->prepare("SELECT * FROM commande WHERE code_barres_cmd =:code_barre");
        $stmt_info_colis->bindParam(':code_barre', $tracking_number);
        $stmt_info_colis->execute();
        $data_info_colis =  $stmt_info_colis->fetch();

        // type cms || 0=>légé , 1=> moyen , 2=> grand

        $count = $stmt_info_colis->rowCount();
        if($count<1){
            $error_msg[]= "Not found object";
        }else{

            $fournisseur = CodeInfoExpediteur($data_info_colis['id_exped']);

            $sate_etat = new stdClass();
            $sate_etat->id_cmd = $data_info_colis['id_cmd'];
            $sate_etat->code_barres_cmd = $data_info_colis['code_barres_cmd'];
            $sate_etat->nom_exped = fix_encode($fournisseur[0]);
            $sate_etat->nom_agence = getInfoAgence($data_info_colis['id_agence'],'nom_agence');
            $sate_etat->date_cmd = date('d-m-Y',$data_info_colis['date_cmd']);;
            $sate_etat->nom_cli = fix_encode($data_info_colis['nom_cli']);
            $sate_etat->adr_cli = fix_encode($data_info_colis['adr_cli']);
            $sate_etat->nbr_colis = fix_encode($data_info_colis['nbr_colis']);
            $sate_etat->etat_cmd = fix_encode($data_info_colis['etat_cmd']);
            $sate_etat->type_cmd = fix_encode(getTaillePieceCmd($data_info_colis['id_cmd']));
            $sate_etat->observ_cmd = fix_encode($data_info_colis['observ_cmd']);
            $commande[] = $sate_etat;
        }
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->commande = $commande;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->status = '';
        $PickupReturn->commande = '';
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//add new runsheet
function AddRunsheet($new_runsheet){
    global $conn;
    $error_msg = array();

    //Pickup data
    $matricule = $new_runsheet->matricule;
    $list_code = $new_runsheet->list_code;
    $list_nbr_colis_recu = $new_runsheet->list_nbr_colis_recu;
    $frs = $new_runsheet->login;
    $pwd = $new_runsheet->pwd;
    $id_livreur = AuthenticationLivreur($frs,$pwd);
    $date_time = date('Y-m-d H:i:s');
    $date = date('Y-m-d');


    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }
    if(trim($id_livreur)==''){
        $error_msg[]= "The -id_livreur- field is mandatory";
    }
    if(trim($matricule)==''){
        $error_msg[]= "The -matricule- field is mandatory";
    }
    if(trim($list_code)==''){
        $error_msg[]= "The -list_code- field is mandatory";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){

        //create id runsheet
        $num_runsheet=getCodeRunsheet().'';
        $date_now=time();
        $id_agent = $id_livreur ;
        $id_agence = GetAgenceLivreur($id_agent);

        $j=0 ;



        $stmt = $conn->prepare("INSERT INTO runsheet (num_runsheet,id_liv_runsheet,mat_voiture_runsheet,date_runsheet,id_agence,id_util) VALUES (:num_runsheet,:id_liv_runsheet,:mat_voiture_runsheet,:date_runsheet,:id_agence,:id_agent)");
        $stmt->bindValue(':num_runsheet', $num_runsheet);
        $stmt->bindValue(':id_liv_runsheet', $id_livreur);
        $stmt->bindValue(':mat_voiture_runsheet', $matricule);
        $stmt->bindValue(':date_runsheet', $date_now);
        $stmt->bindValue(':id_agence', $id_agence);
        $stmt->bindValue(':id_agent', $id_agent);
        $stmt->execute();

        $id_runsheet= $conn->lastInsertId();





        $j++;


        $list_nbr_colis_recu_array = explode(',', $list_nbr_colis_recu);
        $i=0;
        //insert codes
        $list_code_array = explode(',', $list_code);
        foreach ($list_code_array as $value) {

            $stmt = $conn->prepare("SELECT * FROM commande WHERE code_barres_cmd=:code_barres_cmd");
            $stmt->bindParam(':code_barres_cmd', $value);
            $stmt->execute();
            $data =  $stmt->fetch();

            //Insert runsheet_log
            $stmt = $conn->prepare("INSERT INTO runsheet_log(id_run,id_cmd,date_log_runsheet,nbr_colis,nbr_colis_runsheet,annuler_cmd) VALUES (:id_run,:id_cmd,:date_log_runsheet,:nbr_colis,:nbr_colis_runsheet,:annuler_cmd)");
            $stmt->bindValue(':id_run', $id_runsheet );
            $stmt->bindValue(':id_cmd', $data['id_cmd'] );
            $stmt->bindValue(':date_log_runsheet', $date_now );
            $stmt->bindValue(':nbr_colis', $data['nbr_colis'] );
            $stmt->bindValue(':nbr_colis_runsheet', $list_nbr_colis_recu_array[$i] );
            $stmt->bindValue(':annuler_cmd', 0);
            $stmt->execute();

            //change etat and increment tentative
            $stmt = $conn->prepare("UPDATE commande SET nbr_tentative = nbr_tentative+1, etat_cmd = 3 WHERE id_cmd = :id_cmd");
            $stmt->bindValue(':id_cmd', $data['id_cmd']);
            $stmt->execute();

            //Insert commande_log
            $stmt = $conn->prepare("INSERT INTO commande_log(id_cmd,id_agent,type_agent,action_log,type_action,date_log,id_agence,id_agence_dest) VALUES (:id_cmd,:id_agent,:type_agent,:action_log,:type_action,:date_log ,:id_agence,:id_agence_dest )");
            $stmt->bindValue(':id_cmd', $data['id_cmd'] );
            $stmt->bindValue(':id_agent', $id_agent );
            $stmt->bindValue(':type_agent',2 );
            $stmt->bindValue(':action_log', 3 );
            $stmt->bindValue(':type_action', 0 );
            $stmt->bindValue(':date_log', $date_now);
            $stmt->bindValue(':id_agence', $data['id_agence']);
            $stmt->bindValue(':id_agence_dest', $data['id_agence_dest']);
            $stmt->execute();


            $stmtrun = $conn->prepare("SELECT * FROM runsheet WHERE id_runsheet=:id_runsheet");
            $stmtrun->bindValue(':id_runsheet',$id_runsheet);
            $stmtrun->execute();
            $datarun =  $stmtrun->fetch();


            $new_nbr_cmd_runsheet = $datarun['nbr_cmd_runsheet'] + 1 ;
            $new_nbr_colis_runsheet = $datarun['nbr_colis_runsheet'] + $list_nbr_colis_recu_array[$i] ;

            /*$myfile = fopen("newfile.txt", "w") ;
            $txt = $j."\n".$new_nbr_cmd_runsheet."\n".$new_nbr_colis_runsheet."\n".$matricule."\n".$list_code."\n".$list_nbr_colis_recu."\n".$id_livreur."\n".$num_runsheet."\n".$id_agent."\n".$id_agence."\n".$id_runsheet;
            fwrite($myfile, $txt);
            fclose($myfile);*/

            // update runsheet
            $stmtupdatetun = $conn->prepare("UPDATE runsheet SET nbr_cmd_runsheet = :nbr_cmd_runsheet , nbr_colis_runsheet = :nbr_colis_runsheet WHERE id_runsheet = :id_runsheet");
            $stmtupdatetun->bindValue(':id_runsheet',$id_runsheet);
            $stmtupdatetun->bindValue(':nbr_cmd_runsheet',$new_nbr_cmd_runsheet);
            $stmtupdatetun->bindValue(':nbr_colis_runsheet',$new_nbr_colis_runsheet);
            $stmtupdatetun->execute();

            $i++;
        }

    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1 and $id_runsheet!=''){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->id_runsheet = $id_runsheet;

        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->id_runsheet  = '';

        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}


//Update status Shipment
function AddCheque($cheque){
    global $conn;
    $error_msg = array();

    //call data
    $code_barre = $cheque->code_barre;
    $file_name = $cheque->file_name;
    $frs = $cheque->login;
    $pwd = $cheque->pwd;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }
    if(trim($code_barre)==''){
        $error_msg[]= "The -code_barre- field is mandatory";
    }
    if(trim($file_name)==''){
        $error_msg[]= "The -file_name- field is mandatory";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        $id_cmd = GetIdCommande($code_barre);
        //Insert log
        $stmt = $conn->prepare("UPDATE commande SET image_cheque = :file_name WHERE id_cmd = :id_cmd");
        $stmt->bindValue(':id_cmd', $id_cmd);
        $stmt->bindValue(':file_name', $file_name);
        $stmt->execute();
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->etat = 1;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->etat = 0;
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}
//Update Type commande
function UpdateTypeCommande($typecomande){
    global $conn;
    $error_msg = array();

    //call data
    $frs = $typecomande->login;
    $pwd = $typecomande->pwd;
    $code_barre = $typecomande->tracking_number;
    $list_type_cmd = $typecomande->list_type_cmd;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }
    if(trim($code_barre)==''){
        $error_msg[]= "The -code_barre- field is mandatory";
    }
    if(trim($list_type_cmd)==''){
        $error_msg[]= "The -list_type_cmd- field is mandatory";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        $id_cmd = GetIdCommande($code_barre);

        // split taille commande
        $array_type_cmd = explode(',',$list_type_cmd);

        $nbr_colis = count($array_type_cmd) ;

        //update nbr colis
        $stmt = $conn->prepare("UPDATE commande SET nbr_colis = :nbr_colis WHERE id_cmd = :id_cmd");
        $stmt->bindValue(':id_cmd', $id_cmd);
        $stmt->bindValue(':nbr_colis', $nbr_colis);
        $stmt->execute();


        // delete type commande
        $stmt = $conn->prepare("DELETE FROM commande_ligne WHERE id_cmd = :id_cmd");
        $stmt->bindValue(':id_cmd', $id_cmd);
        $stmt->execute();

        for($i=0; $i < count($array_type_cmd) ; $i++) {
            //Insert commande_ligne
            $stmt = $conn->prepare("INSERT INTO commande_ligne(id_cmd, taille) VALUES (:id_cmd,:taille)");
            $stmt->bindValue(':id_cmd', $id_cmd);
            $stmt->bindValue(':taille', $array_type_cmd[$i]);
            $stmt->execute();
        }
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->etat = 1;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        $PickupReturn->etat = 0;
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

//Check access create runsheet
function CheckPasswordRunsheet($access){
    global $conn;
    $error_msg = array();

    //livreur data
    $frs = $access->login;
    $pwd = $access->pwd;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){

       /* $livreur = AuthenticationLivreur($frs,$pwd);
        //info expediteur
        $stmt_sms = $conn->prepare("SELECT * FROM sms WHERE id =1");
        $stmt_sms->execute();
        $data_sms =  $stmt_sms->fetch();

        $count = $stmt_sms->rowCount();
        if($count<1){
            $error_msg[]= "Not found object";
        }*/
    }

    //if is OK
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 0;
        $PickupReturn->ErrorsTxt = $error_msg;
        return $PickupReturn ;
    }else{//Some error...
        $PickupReturn = new stdClass;
        $PickupReturn->HasErrors = 1;
        $PickupReturn->ErrorsTxt = $error_msg;
        return $PickupReturn ;
    }
    //Free memory
    $stmt = null;
    $conn = null;
}

function AddZone($zone){
   

    global $conn;
    $error_msg = array();

    //call data
    $frs = $zone->login;
    $pwd = $zone->pwd;
    $zone_name = $zone->zone_name;

    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }

    $id_livreur = AuthenticationLivreur($frs,$pwd);

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){


        $stmt = $conn->prepare("INSERT INTO livreur_zone (id_liv, zone_name) VALUES (:id_liv,:zone_name)");


        $stmt->bindValue(':id_liv', $id_livreur);
        $stmt->bindValue(':zone_name',$zone_name);
        $stmt->execute();
        $id_zone = $conn->lastInsertId();




    }
 
    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $zoneResponse = new stdClass;
        $zoneResponse->HasErrors = 0;
        $zoneResponse->ErrorsTxt = $error_msg;
        $zoneResponse->id_zone = $id_zone;
        return $zoneResponse ;
    }else{//Some error...
        $zoneResponse = new stdClass;
        $zoneResponse->HasErrors = 1;
        $zoneResponse->ErrorsTxt = $error_msg;
        $zoneResponse->id_zone = 0;
        return $zoneResponse ;
    }
    //Free memory
    $stmt = null;
    $conn = null;

}
function AddZoneLivColis($zone){


    global $conn;
    $error_msg = array();

    //call data
    $frs = $zone->login;
    $pwd = $zone->pwd;
    $id_zone = $zone->id_zone;
    $cad_colis = $zone->cad_colis;
    $id_cmd = GetIdCommande($cad_colis);
    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }



    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){


        //update nbr colis
        $stmt = $conn->prepare("UPDATE commande SET id_zone_liv = :id_zone_liv WHERE id_cmd = :id_cmd");
        $stmt->bindValue(':id_cmd', $id_cmd);
        $stmt->bindValue(':id_zone_liv', $id_zone);
        $stmt->execute();




    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
        $zoneResponse = new stdClass;
        $zoneResponse->HasErrors = 0;
        $zoneResponse->ErrorsTxt = $error_msg;
        $zoneResponse->etat = 0;
        return $zoneResponse ;
    }else{//Some error...
        $zoneResponse = new stdClass;
        $zoneResponse->HasErrors = 1;
        $zoneResponse->ErrorsTxt = $error_msg;
        $zoneResponse->etat = 1;
        return $zoneResponse ;
    }
    //Free memory
    $stmt = null;
    $conn = null;

}



function CreateNewPickup($zone){



        $error_msg[]= "Error!!";


    $zoneResponse = new stdClass;
    $zoneResponse->HasErrors = 1;
    $zoneResponse->ErrorsTxt = $error_msg;
    $zoneResponse->id_ram = 0;
    return $zoneResponse ;




    global $conn;
    $error_msg = array();

    //call data
    $frs = $zone->login;
    $pwd = $zone->pwd;


    //Control data
    if(!AuthenticationLivreur($frs,$pwd)){
        $error_msg[]= "Failed authentication";
    }

    $id_livreur = AuthenticationLivreur($frs,$pwd);

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        $id_agence=getInfoLivreur($id_livreur,'id_agence');
        $date_ram=date('Y-m-d');
        $start_ram=date('H');
        $date_creat_ram=time();
        $stmt = $conn->prepare("INSERT INTO `ramassage` (id_livreur, date_ram, start_ram,date_creat_ram,id_agence,type_ram) VALUES (:id_liv , :date_ram, :start_ram, :date_creat_ram, :id_agence, 2)");


        $stmt->bindValue(':id_liv', $id_livreur);
        $stmt->bindValue(':date_ram',$date_ram);
        $stmt->bindValue(':start_ram',$start_ram);
        $stmt->bindValue(':date_creat_ram',$date_creat_ram);
        $stmt->bindValue(':id_agence',$id_agence);
        $stmt->execute();
        $id_ram = $conn->lastInsertId();




    }

    if(AuthenticationLivreur($frs,$pwd) and count($error_msg)<1){
        // Object-styled definition of a Pickup status Return
//        $zoneResponse = new stdClass;
//        $zoneResponse->HasErrors = 0;
//        $zoneResponse->ErrorsTxt = $error_msg;
//        $zoneResponse->id_ram = $id_ram;
//        return $zoneResponse ;


        // demande de chedy

        $zoneResponse = new stdClass;
        $zoneResponse->HasErrors = 1;
        $zoneResponse->ErrorsTxt = $error_msg;
        $zoneResponse->id_ram = 0;
        return $zoneResponse ;



    }else{//Some error...
        $zoneResponse = new stdClass;
        $zoneResponse->HasErrors = 1;
        $zoneResponse->ErrorsTxt = $error_msg;
        $zoneResponse->id_ram = 0;
        return $zoneResponse ;
    }
    //Free memory
    $stmt = null;
    $conn = null;

}




// initialize SOAP Server
$server=new SoapServer("serviceDelivery.wsdl");

// register available functions
$server->addFunction(['UpdateShipmentStatus','ListRunsheet','ListParcelRunsheet','LogCall','ListPickUp','UpdatePickupStatus','TrackShipment','Authentication','GetSms','InfoParcel','AddRunsheet','AddCheque','UpdateTypeCommande','GetObservation','AddZone','AddZoneLivColis','CreateNewPickup']);
// start handling requests
$server->handle();