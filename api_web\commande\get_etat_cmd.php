<?php
$entityBody = json_decode(file_get_contents('php://input'),true);

$logFile = 'payload_log_etat.txt';
file_put_contents($logFile, json_encode($entityBody) . "\n",FILE_APPEND);
$myfile = fopen("newfile.txt", "w") or die("Unable to open file!");
fwrite($myfile, (''));
////echo json_encode(array("num_suivi_cmd" => $entityBody['nom_cli'],"url_bl"=>'https://localhost/livraison/composants/mc_commande/exportCommandePdf.php?id='.$entityBody['nom_cli']));
fclose($myfile);

header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
// include database and object files
include_once '../config/database.php';
include_once '../objects/commande.php';
 //get database connection
$database = new Database();
$db = $database->getConnection();
$commande = new commande($db);
$commande->num_suivi_cmd='';
$length_arr=count((array)$entityBody['num_suivi_cmd']);
$verg=',';
    foreach ((array)$entityBody['num_suivi_cmd'] as $key => &$value) {
        if($key+1==$length_arr){
           $verg='';
        }
        $commande->num_suivi_cmd = $commande->num_suivi_cmd.$value.$verg;
    }
if($commande->get_etat_cmd()){
    echo json_encode($commande->etat_cmd_arr);

}else{
    echo json_encode(array("message" => "Colis non trouvé.","success"=>0));
}

?>