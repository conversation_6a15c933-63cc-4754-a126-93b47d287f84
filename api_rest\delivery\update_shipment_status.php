<?php
// Get JSON input
$entityBody = json_decode(file_get_contents('php://input'), true);

// Required headers
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Include database and object files
include_once '../config/database.php';
include_once '../objects/delivery.php';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Initialize delivery object
$delivery = new Delivery($db);

// Validate input
if(!isset($entityBody['login']) || !isset($entityBody['pwd'])){
    echo json_encode(array(
        "HasErrors" => 1,
        "ErrorsTxt" => array("Missing login or password"),
        "success" => 0
    ));
    exit;
}

if(!isset($entityBody['tracking_number']) || !isset($entityBody['etat'])){
    echo json_encode(array(
        "HasErrors" => 1,
        "ErrorsTxt" => array("Missing tracking_number or etat"),
        "success" => 0
    ));
    exit;
}

// Update shipment status
$result = $delivery->update_shipment_status($entityBody);

// Return response
echo json_encode($result);
?>
