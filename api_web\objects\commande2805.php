<?php
include_once '../objects/authentification.php';


class Commande{

    // database connection and table name
    private $conn;

    // constructor with $db as database connection
    public function __construct($db){
        $this->authentification =  new authentification($db);
         $verif_auth=$this->authentification->authentification_user();
         if($verif_auth==0){
             exit;
        }
        
        $this->conn = $db;

    }
    
    //insertion colis
    function add_colis(){

        $tabVille=['1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24'];

        $this->date_cmd=time();
        $this->id_exp=$this->authentification->id_exp;


        $telCli=str_replace(' ','',$this->tel_cli);

        $telCli2=str_replace(' ','',$this->tel_cli2);


        $telCli=substr($telCli, -8);
        $telCli2=substr($telCli2, -8);
 
      //  $nom_agent= $this->getInfoAgence($id_agence,'nom_agence');

//        $this->error=$this->tel_cli;
//        return false;

         if($this->verifTel($telCli)) { // si le 1er numero contient 8 chiffres
            if ($telCli != '00000000') {
                if(!$this->verifTel($telCli2)) {
                    $telCli2 = '';
                }
                if (!in_array($this->ville_cli, $tabVille)) {
                    $this->error = "Ville incorrect";
                    return false;
                }
                    //insertion clent
                $query_insert_client = "insert into commande_client (
                nom_cli,
                adr_cli,
                ville_cli,
                tel_cli,
                tel_cli2,
                tel_cli3,
                id_exped
                )values(
                '" . $this->nom_cli . "',      
                '" . $this->adr_cli . "',      
                '" . $this->ville_cli . "',      
                '" . $telCli . "',      
                '" . $telCli2 . "',      
                '" . $this->tel_cli3 . "',      
                '" . $this->id_exp . "'     
             )";
                $stmt_insert_client = $this->conn->prepare($query_insert_client);
                $stmt_insert_client->execute();
                $this->id_cli = $this->conn->lastInsertId();

                //fin insertion client

                $this->id_agence=$this->authentification->id_agence;
                // $this->id_agence=1;
                $this->id_agence_dest=$this->get_agDest($this->ville_cli);


                $contenueColis=$this->ContenuColis;



                //insertion commande
                $query_insert = "insert into commande (
                id_client,
                nom_cli,
                adr_cli,
                ville_cli,
                tel_cli,
                tel_cli2,
                ttc_cmd,
                nbr_colis,
                id_exped,
                id_agence,
                id_agence_origine,
                id_agence_dest,
                date_cmd,
                observ_cmd, 
                code_barres_ext,
                contenu_cmd,
                echange_cmd,
                fragile,
                echange_ancienne_cmd,
                code_suivie_revendeur,
                autorisation_ouv,
                autorisation_chq,
                create_with_api
             )values(
                '" . $this->id_cli . "',      
                '" . $this->nom_cli . "',      
                '" . $this->adr_cli . "',
                '" . $this->ville_cli . "', 
                '" . $telCli . "',
                '" . $telCli2 . "',
                '" . $this->ttc_cmd . "',
                '" . $this->nbr_colis . "' ,
                '" . $this->id_exp . "' ,
                '" . $this->id_agence . "' ,
                '" . $this->id_agence . "' ,
                '" . $this->id_agence_dest . "' ,
                '" . $this->date_cmd . "' ,
                '" . $this->commentaire_cmd . "' , 
                '" . $this->code_barres_ext . "' ,
                '" . $this->ContenuColis . "' ,
                '" . $this->echange_cmd . "' ,
                '" . $this->fragile . "' ,
                '" . $this->ancienne_commande . "' ,
                 '" . $this->code_suivie_revendeur . "' ,
                 '" . $this->autorisation_ouv . "' ,
                 '" . $this->autorisation_chq . "' ,
                 '1' 
             )";




                $stmt_insert = $this->conn->prepare($query_insert);
                $stmt_insert->execute();
                $this->id_cmd = $this->conn->lastInsertId();
                //fin insertion commande

                //génération du code barres format-> AAMMJJid_expedId_commande
                $this->code_barres_cmd = date('ymd', time()).str_pad($this->id_cmd, 8, '0', STR_PAD_LEFT);
                $reqCodeCmd="update commande set   code_barres_cmd='".$this->code_barres_cmd."'  where id_cmd='".$this->id_cmd."'";
                $stmt_code_cmd = $this->conn->prepare($reqCodeCmd);
                $stmt_code_cmd->execute();


                //insertion colis d'échange
                //insertion du produit a échangé
                if($this->echange_cmd=='1') {
                    $this->code_barres_produit = '00'.date('ymd', time()).str_pad($this->id_cmd, 5, '0', STR_PAD_LEFT);

                    $reqInsertEch ="insert into produit_echange (
                            nom_produit,
                            id_cmd,
                            id_exp,
                            date_produit,
                            etat_produit,
                            agence_produit,
                            agence_origine_produit,
                            agence_destination_produit,
                            code_barres_produit
                         )values(
                            '" . $this->produit_arecevoir . "',
                            '" . $this->id_cmd . "',
                            '" . $this->id_exp . "',
                            '" . $this->date_cmd . "',
                            '0',
                            '" . $this->id_agence . "',
                            '" . $this->id_agence . "',
                            '" . $this->id_agence_dest . "',
                            '" .  $this->code_barres_produit . "'
                         )";
                    $stmt_insertEch = $this->conn->prepare($reqInsertEch);
                    $stmt_insertEch->execute();
                }
                //fin insertion colis d'échange
                $tarif_livraison=0;
                $nb_colis_petit=0;
                $nb_colis_moy=0;
                $nb_colis_grand=0;
                $tarif_retour=0;
                //insertion des piéces
                $tabTaille=array();
                $tabTaille=explode('/',$this->type_colis_tab) ;
                for ($z = 0; $z < $this->nbr_colis; $z++) {
                    $type_colis = intval($tabTaille[$z]);


                    $sqlExpC = "SELECT * FROM expediteur WHERE id_exp = :id_exp";
                    $stmtExpC = $this->conn->prepare($sqlExpC);
                    $stmtExpC->bindParam(':id_exp', $this->id_exp);
                    $stmtExpC->execute();
                    $resVerifCli = $stmtExpC->fetch(PDO::FETCH_ASSOC);




                    // Livraison
                    $prix_livr_leg = $resVerifCli['prix_livr_leg'];
                    $prix_livr_moy = $resVerifCli['prix_livr_moy'];
                    $prix_livr_grd = $resVerifCli['prix_livr_grd'];

                    // Retour
                    $prix_retour_leg = $resVerifCli['prix_retour_leg'];
                    $prix_retour_moy = $resVerifCli['prix_retour_moy'];
                    $prix_retour_grd = $resVerifCli['prix_retour_grd'];



                    switch ($type_colis){
                        case '0':
                            $tarif_livraison+=$prix_livr_leg;
                            $tarif_retour+=$prix_retour_leg;
                            $nb_colis_petit++;
                            break;
                        case '1':
                            $tarif_livraison+=$prix_livr_moy;
                            $tarif_retour+=$prix_retour_moy;
                            $nb_colis_moy++;
                            break;
                        case '2':
                            $tarif_livraison+=$prix_livr_grd;
                            $tarif_retour+=$prix_retour_grd;
                            $nb_colis_grand++;
                            break;
                    }




                    $reqInsertEch = "INSERT INTO commande_ligne(
                    id_cmd,
                    taille
                ) VALUES ( 
                    $this->id_cmd,
                    $type_colis
                    )";
                    $stmt_insertEch = $this->conn->prepare($reqInsertEch);
                    $stmt_insertEch->execute();
                }


                $res_exped=0;
                //if($this->id_exp==14) {
                    $with_patent=$this->getInfVendeur($this->id_exp,'with_patent');
                    if($with_patent==1) {

                        $valRs = $this->ttc_cmd - $tarif_livraison;
                        $res_exped = ($valRs * 3) / 100; // 3%
                    }
                //}

                $reqUpdateTarifCmd = "UPDATE commande SET res_exped = :res_exped, tarif_livraison = :tarif_livraison, tarif_retour = :tarif_retour, nb_colis_petit = :nb_colis_petit, nb_colis_moy = :nb_colis_moy, nb_colis_grand = :nb_colis_grand WHERE id_cmd = :id_last";
                $stmtUpdateTarifCmd = $this->conn->prepare($reqUpdateTarifCmd);

                $stmtUpdateTarifCmd->bindParam(':res_exped', $res_exped);
                $stmtUpdateTarifCmd->bindParam(':tarif_livraison', $tarif_livraison);
                $stmtUpdateTarifCmd->bindParam(':tarif_retour', $tarif_retour);
                $stmtUpdateTarifCmd->bindParam(':nb_colis_petit', $nb_colis_petit);
                $stmtUpdateTarifCmd->bindParam(':nb_colis_moy', $nb_colis_moy);
                $stmtUpdateTarifCmd->bindParam(':nb_colis_grand', $nb_colis_grand);
                $stmtUpdateTarifCmd->bindParam(':id_last', $this->id_cmd);
                $stmtUpdateTarifCmd->execute();





                //fin insertion piéce




                //insertion log creation commande

                $reqInsertLog ="insert into commande_log (
                            id_cmd,
                            id_exp,
                            id_agent,
                            type_agent,
                            action_log,
                            type_action,
                            date_log,
                            id_agence,
                            id_agence_dest
                         )values(
                            '" . $this->id_cmd . "',
                            '" . $this->id_exp . "',
                            '" . $this->id_exp . "',
                            '0',
                            '0',
                            '0',
                            '" . $this->date_cmd . "',
                            '" . $this->id_exp . "',
                            '" . $this->id_agence_dest . "'
                         )";
                $stmt_insertLog = $this->conn->prepare($reqInsertLog);
                $stmt_insertLog->execute();

                //fin insertion log creation commande


                //////////////////////////////////////Amena API
                //if($this->ville_cli=='14' or $this->ville_cli=='6' or $this->ville_cli=='18' or $this->ville_cli=='9' or $this->ville_cli=='22' or $this->ville_cli=='10' or $this->ville_cli=='5' or $this->ville_cli=='21' ){

                //$dossier_api_tr='amena';
                //$id_cmd=$this->id_cmd;
                //  echo '../../includes/api/'.$dossier_api_tr.'/createShipment/createShipments.php';
                //include('../commande/createShipmentsAmena.php');

                //}



                ////////////////////////////////////// AFEX tr
                if($this->ville_cli=='14' or $this->ville_cli=='6' or $this->ville_cli=='18' or $this->ville_cli=='9' or $this->ville_cli=='22' or $this->ville_cli=='10' or $this->ville_cli=='5' or $this->ville_cli=='21' or $this->ville_cli=='7' or $this->ville_cli=='2' or $this->ville_cli=='19' or $this->ville_cli=='11'  ){
                    $dossier_api_tr='AFEX';
                    $id_cmd=$this->id_cmd;
          //          include('../commande/createShipmentsAFEX.php');
                }
                // afex kairouwn
                if($this->ville_cli=='8' ){
                    $dossier_api_tr='AFEX';
                    $id_cmd=$this->id_cmd;
            //        include('../commande/createShipmentsAFEX_2.php');
                }


                ////////////////////////////////////// Jola tr
//                if($this->ville_cli=='8' ){
//                    $dossier_api_tr='jola';
//                    $id_cmd=$this->id_cmd;
//                    //include('../commande/createShipmentsJola.php');
//                }





                if($this->id_cmd!=null){
                    return true;
                }
            }
        }else{
             $this->error = "N° incorrect ".$telCli;
             return false;
         }




    }
    
       // get the details of commande by num_suivi_cmd
function get_etat_cmd() {
    $this->etat_cmd_arr = array();

    $array_ = explode(",", $this->num_suivi_cmd);
    $array_ = array_unique(array_slice($array_, 0, 100));
    $placeholders = implode(',', array_fill(0, count($array_), '?'));

    $query = "SELECT * FROM commande WHERE code_barres_cmd IN ($placeholders)";
    $stmt = $this->conn->prepare($query);
    $stmt->execute($array_);

    if ($stmt->rowCount() > 0) {
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $this->code_cmd = $row['code_barres_cmd'];
            $this->id_cmd = $row['id_cmd'];
            $this->etat_retour = $row['etat_retour'];
            $this->etat_cmd = $row['etat_cmd'];
            $this->etat_paye = $row['etat_paye_exped'];
            $this->date_livraison_retour = $row['date_livraison_retour'];
            $this->etat_validation = $row['etat_validation'];
            $this->id_paiement = $row['id_paiement'];

            $date_valide = '';
            if ($this->etat_paye == 1) {
                $queryPai = "SELECT * FROM paiement WHERE id = ?";
                $stmtPai = $this->conn->prepare($queryPai);
                $stmtPai->execute([$this->id_paiement]);
                $rowPai = $stmtPai->fetch(PDO::FETCH_ASSOC);
                if ($rowPai) {
                    $date_valide = date('Y-m-d', $rowPai['date']);
                }
            }

            $this->lib_etat_cmd = match ($this->etat_cmd) {
                0 => "En attente",
                1 => "Ramassé",
                2 => "Au dépôt",
                3 => "En livraison",
                4 => "Reporté",
                5 => "Livré",
                6 => "Retour",
                default => "",
            };

            $this->lib_etat_paye = '';
            if ($this->etat_cmd == 5) {
                $this->lib_etat_paye = $this->etat_paye == 1 ? " payé" : " non payé";
            }

            $this->lib_etat_retour = '';
            if ($this->etat_cmd == 6) {
                $this->lib_etat_retour = match ($this->etat_retour) {
                    0 => " définitif",
                    1 => " agence origine",
                    2 => " expéditeur",
                    default => "",
                };
                $this->lib_etat_retour .= $this->etat_validation == 0 ? " en attente de validation" : " validé";
            }

            $this->lib_etat = $this->lib_etat_cmd . $this->lib_etat_paye . $this->lib_etat_retour;

            $this->motif_retour = '';
            if (in_array($this->etat_cmd, [4, 6])) {
                $reqmotif = "SELECT * FROM commande_log WHERE id_cmd = ? AND type_action = 0 AND (action_log = 4 OR action_log = 6)";
                $stmtMotif = $this->conn->prepare($reqmotif);
                $stmtMotif->execute([$this->id_cmd]);
                $resMotif = $stmtMotif->fetch(PDO::FETCH_ASSOC);
                if ($resMotif) {
                    $this->motif_retour = $resMotif['observation'];
                }
            }

            $this->etat_cmd_arr[] = array(
                "code_barres_cmd" => $this->code_cmd,
                "etat_cmd" => $this->lib_etat,
                "code_etat_cmd" => $this->etat_cmd,
                "etat_validation_retour" => $this->etat_validation,
                "date_livraison_retour" => $this->date_livraison_retour,
                "motif" => $this->motif_retour,
                "etat_paye" => $this->etat_paye,
                "etat_retour" => $this->etat_retour,
                "date_paiement" => $date_valide
            );
        }
        return true;
    }

    return false;
}




    function verifTel($chaine) {
        $pattern = '/^\d{8}$/';
        if (preg_match($pattern, $chaine)) {
            return true;
        } else {
            return false;
        }
    }

      function tracking_cmd(){
       $this->log_arr=array();
        $this->id_cmd=$this->getInfoCmd($this->num_suivi_cmd,'id_cmd');
        $this->etat_cmd=$this->getInfoCmd($this->num_suivi_cmd,'etat_cmd');
        $this->id_exped=$this->getInfoCmd($this->num_suivi_cmd,'id_exped');
        $this->id_agence_cmd=$this->getInfoCmd($this->num_suivi_cmd,'id_agence');
        $nom_agence_cmd=$this->getInfoAgence($this->id_agence_cmd,'nom_agence');
        // query to read single record
        $query = "select * from commande_log where id_cmd = ?";

        // prepare query statement
        $stmt = $this->conn->prepare( $query );

        // bind id of product to be updated
        $stmt->bindParam(1,$this->id_cmd);

        // execute query
        $stmt->execute();
        // get retrieved row
        while($row = $stmt->fetch(PDO::FETCH_ASSOC)){
            $this->idlog=$row['id_log'];
            $type_action=$row['type_action'];
            $action_log=$row['action_log'];
            $id_agent=$row['id_agent'];
            $id_agence_inter=$row['id_agence'];
            $id_agence_inter_dest=$row['id_agence_dest'];
            $type_agent=$row['type_agent'];
            $date_log=date('d-m-Y H:i',$row['date_log']);
            $heure_log=date('H:i',$row['date_log']);
            $type_agent=$row['type_agent'];
            $nom_agent=$this->get_nom_utlisateur_log($id_agent,$type_agent);

            if($type_action=='0'){ //log_etat_commande 
                    switch ($action_log) {
                        case 0:
                                $description= "Créé par ".$nom_agent;
                                break; 
                        case 1:
                                $description= "Ramassé par ".$nom_agent.' vers '.$nom_agence_cmd;
                                break; 
                        case 2:
                                $description= "Reçu par ".$nom_agent ;
                                break; 

                        case 3:
                                $description= "Sortie du ".$nom_agent;
                                break;  
                        case 4:
                                $description= "Reporté";
                                break;
                        case 5:
                                $description= "Livré Par ".$nom_agent;
                                break;
                         case 6:
                                $description= "Retour Par ".$nom_agent;
                                break; 
                    }
                }else if($type_action=='1'){ //log_etat_retour
                     switch ($action_log) {
                         case 0:
                                $description= "Retour au dépôt par =".$nom_agent;
                                break; 
                         case 1:
                                $description= "Reçue par ".$nom_agent;                                     
                                break; 
                         case 2:
                                 $description= "Reçue par ".$this->getInfVendeur($this->id_exped,'nom_exp');
                                break; 

                    }
                }else if($type_action=='2'){ //log_etat_paiement
                     if($this->etat_cmd==5){
                     switch ($action_log) {
                            case 0:
                                $description= "Paiement";                                   
                                break; 
                            case 1:
                                $description= "Préparation paiement";                                   
                                break; 
                            case 2:
                                $description= "Paiement validé";                                   
                                break; 
                        }
                        
                     }else{
                          switch ($action_log) {
                          case 0:
                                $description= "Réglement";                                   
                                break; 
                            case 1:
                                $description= "Préparation frais de livraison";                                   
                                break; 
                            case 2:
                                $description= "Facture validée ";                                   
                                break; 
                          }
                         
                     }
                }else if($type_action=='3'){ //inetr_depot
                    switch ($action_log) {
                        case 0:
                            $description= "Transmit de ".$this->getInfoAgence($id_agence_inter,'nom_agence').' à '.$this->getInfoAgence($id_agence_inter_dest,'nom_agence');
                            break;
                        case 1:
                            $description= "Accepté par ".$this->getInfoAgence($id_agence_inter_dest,'nom_agence').' envoyé par '.$this->getInfoAgence($id_agence_inter,'nom_agence');
                            break;


                    }
                }
              array_push($this->log_arr,array(
                        "date_log" => $date_log,
                        "description" => $description
                        ));
        }
  
        if(count($row)>0){
            return true;
        }
        return false;
    }
     function get_agDest($villeCli){
        $reqAd=("select * from ville_agence WHERE id_ville=".$villeCli);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $id_agence_dest=$resAd['id_agence'];
    }   

    function getInfoAgence($id,$type){
        $reqAd=("select * from agence WHERE id_agence=".$id);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $resAd[$type];
    }  

    function getInfoLivreur($id,$type){
        $reqAd=("select * from livreur WHERE id_livreur=".$id);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $resAd[$type];
    } 
    

    function getInfoAgent_agence($id,$type){
        $reqAd=("select * from agent WHERE id_agent=".$id);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $resAd[$type];
    } 
    

    function getInfVendeur($id,$type){
        $reqAd=("select * from expediteur WHERE id_exp=".$id);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $resAd[$type];
    } 
     function getInfoCmd($code,$type){
        $reqAd=("select * from commande WHERE code_barres_cmd=".$code);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $resAd[$type];
    } 
    
    function get_nom_utlisateur_log($id_agent,$type_agent){
       switch ($type_agent) { //get_nom_utlisateur_log
            case 0:
                $nom_agent= $this->getInfVendeur($id_agent,'nom_exp');
                break; 
            case 1:
                $id_agence=$this->getInfoAgent_agence($id_agent,'id_agence');
                $nom_agent= $this->getInfoAgence($id_agence,'nom_agence').' ('. $this->getInfoAgent_agence($id_agent,'nom_agent').')';
                break; 
            case 2:
                $nom_agent= $this->getInfoLivreur($id_agent,'nom_livreur');
                break; 
        }
        return $nom_agent;
    }
    function encryptIt($string)
    {
        $output = false;

        $encrypt_method = "AES-256-CBC";
        $secret_key = 'This is my secret key';
        $secret_iv = 'This is my secret iv';

        // hash
        $key = hash('sha256', $secret_key);

        // iv - encrypt method AES-256-CBC expects 16 bytes - else you will get a
        // warning
        $iv = substr(hash('sha256', $secret_iv), 0, 16);

        $output = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
        $output = base64_encode($output);

        return $output;
    }
    
}
function clean_chaine($str){
    $caractere = array("'",".", '"');
    $remplace = array(" "," ", " ");
    $str = str_replace($caractere, $remplace, $str);
    return trim($str);
}

