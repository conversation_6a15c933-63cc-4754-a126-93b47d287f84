<?php
//$myfile = fopen("newfile.txt", "w") or die("Unable to open file!");
$entityBody = json_decode(file_get_contents('php://input'),true);

$logFile = 'payload_log.txt';
file_put_contents($logFile, json_encode($entityBody) . "\n", FILE_APPEND);
$myfile = fopen("newfile.txt", "w") or die("Unable to open file!");
fwrite($myfile, (''));
////echo json_encode(array("num_suivi_cmd" => $entityBody['nom_cli'],"url_bl"=>'https://localhost/livraison/composants/mc_commande/exportCommandePdf.php?id='.$entityBody['nom_cli']));
fclose($myfile);
 //required headers 
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");
/*ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);*/
// include database and object files
include_once '../config/database.php';
include_once '../objects/commande.php';


 //get database connection
$database = new Database();
$db = $database->getConnection();
//
//// prepare product object
$commande = new commande($db);
//
$commande->nom_cli = urldecode($entityBody['nom_cli']) ;
$commande->nom_cli =addslashes($commande->nom_cli);
$commande->adr_cli = urldecode($entityBody['adr_cli']);
$commande->adr_cli =addslashes($commande->adr_cli);
$commande->ville_cli = urldecode($entityBody['ville_cli']);
$commande->tel_cli = urldecode($entityBody['tel_cli']);
$commande->tel_cli2 = urldecode($entityBody['tel_cli2']);
$commande->tel_cli3 = urldecode($entityBody['tel_cli3']);
$commande->nbr_colis = urldecode($entityBody['nbr_colis']);
$commande->ContenuColis =urldecode($entityBody['ContenuColis']);
$commande->ContenuColis =addslashes($commande->ContenuColis);
$commande->type_colis_tab =urldecode($entityBody['type_colis_tab']);
$commande->ttc_cmd = urldecode($entityBody['ttc_cmd']);
$commande->fragile = urldecode($entityBody['fragile']);
$commande->code_suivie_revendeur = urldecode($entityBody['code_suivie_revendeur']);
$commande->autorisation_ouv = urldecode($entityBody['autorisation_ouv']);
$commande->autorisation_chq = urldecode($entityBody['autorisation_chq']);
$commande->code_barres_ext = urldecode($entityBody['code_barres_ext']) ;
$commande->echange_cmd = urldecode($entityBody['echange_cmd']);
$commande->ancienne_commande = urldecode($entityBody['ancienne_commande_echange']);
$commande->produit_arecevoir = urldecode($entityBody['produit_arecevoir']);
$commande->produit_arecevoir =addslashes($commande->produit_arecevoir);

$commande->commentaire_cmd = urldecode($entityBody['commentaire_cmd']);
$commande->commentaire_cmd =addslashes($commande->commentaire_cmd);
if($commande->add_colis()){
    echo json_encode(array("num_suivi_cmd" => $commande->code_barres_cmd,"url_bl"=>'https://my.adex.tn/BL/'.$commande->encryptIt($commande->id_cmd)));//,"sql" => $commande->sqlTest
}else{
    echo json_encode(array("message" => "UNABLE TO ADD COLIS.".$commande->error,"success"=>0));
}

?>