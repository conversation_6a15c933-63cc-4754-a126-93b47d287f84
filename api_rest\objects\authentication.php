<?php
class Authentication{

    // database connection and table name
    private $conn;
    public $id_livreur;
    public $login;
    public $password;
    public $id_agence;
    public $arr_info_user;

    // constructor with $db as database connection
    public function __construct($db){
        $this->conn = $db;
    }

    // authenticate livreur (delivery person)
    function authenticate_livreur($login, $password){
        $passwordLivreur = md5("AdMiN189&#ç".md5($password));
        $stmt = $this->conn->prepare("SELECT * FROM livreur WHERE login_livreur=:login and password_livreur=:pwd");
        $stmt->bindParam(':login', $login);
        $stmt->bindParam(':pwd', $passwordLivreur);
        $stmt->execute();
        $data = $stmt->fetch();
        $count = $stmt->rowCount();
        
        if($count){
            $this->id_livreur = $data['id_livreur'];
            $this->id_agence = $data['id_agence'];
            $this->arr_info_user = $data;
            return $data['id_livreur'];
        }else{
            return false;
        }
    }

    // get agence of livreur by id
    function get_agence_livreur($id){
        $stmt = $this->conn->prepare("SELECT * FROM livreur WHERE id_livreur=:id");
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        $data = $stmt->fetch();
        $count = $stmt->rowCount();
        
        if($count){
            return $data['id_agence'];
        }else{
            return false;
        }
    }

    // get full authentication info for livreur
    function get_authentication_info($login, $password){
        $livreur_id = $this->authenticate_livreur($login, $password);
        
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "livreur_info" => null,
                "zones" => array()
            );
        }

        // Get livreur info
        $stmt_liv = $this->conn->prepare("SELECT * FROM livreur WHERE id_livreur=:id");
        $stmt_liv->bindParam(':id', $livreur_id);
        $stmt_liv->execute();
        $data_liv = $stmt_liv->fetch();

        // Get zones for livreur
        $stmt_zone = $this->conn->prepare("SELECT * FROM livreur_zone WHERE id_liv=:id");
        $stmt_zone->bindParam(':id', $livreur_id);
        $stmt_zone->execute();
        
        $array_zone = array();
        while($data_zone = $stmt_zone->fetch()) {
            $zone_info = new stdClass();
            $zone_info->id_zone = $data_zone['id_zone'];
            $zone_info->nom_zone = $data_zone['nom_zone'];
            $array_zone[] = $zone_info;
        }

        return array(
            "HasErrors" => 0,
            "ErrorsTxt" => array(),
            "livreur_info" => $data_liv,
            "zones" => $array_zone
        );
    }

    // validate authentication for API requests
    function validate_request_auth($request_data){
        if(!isset($request_data['login']) || !isset($request_data['pwd'])){
            return false;
        }
        
        return $this->authenticate_livreur($request_data['login'], $request_data['pwd']);
    }
}
?>
