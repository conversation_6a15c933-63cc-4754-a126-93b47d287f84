<?php
class authentification{

    // database connection and table name
    private $conn;

    // constructor with $db as database connection
    public function __construct($db){
        $this->conn = $db;
    }

    // read products
    function authentification_user(){
        
        if (!isset($_SERVER['PHP_AUTH_USER'])) {
            header('WWW-Authenticate: Basic realm="Restricted area"');
            header('HTTP/1.0 401 Unauthorized');
            exit;
        }else{
            $this->login=$_SERVER['PHP_AUTH_USER'];
            $this->password=md5("AdMiN189&#ç".md5($_SERVER['PHP_AUTH_PW']));
            // select all query
            $query = "select * from expediteur where login_exp='".$this->login."' and mp_exp='".$this->password."' and etat_exp = '1'";
            $stmt = $this->conn->prepare($query);
            // execute query
            $stmt->execute();
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->id_exp=$row['id_exp'];
            $this->id_agence=$row['id_agence'];

            
            $this->arr_info_user=array(
                "id_exp" => $row['id_exp'],
                "nom_exp" => $row['nom_exp'],
                "id_agence" => $row['id_agence'],
                "adr_exp" => $row['adr_exp']);
            // query products
            return $num = $stmt->rowCount();
        }
    }


}