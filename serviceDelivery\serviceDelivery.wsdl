<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  name="serviceDelivery"
                  targetNamespace="serviceDelivery"
                  xmlns:tns="serviceDelivery">

    <xsd:documentation></xsd:documentation>

    <wsdl:types>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="serviceDelivery">
            <xsd:complexType name="pickup">
                <xsd:all>
                    <xsd:element name="code_barre" type="xsd:string"></xsd:element>
                    <xsd:element name="frs" type="xsd:string"></xsd:element>
                    <xsd:element name="id_frs" type="xsd:integer"></xsd:element>
                    <xsd:element name="agence" type="xsd:integer"></xsd:element>
                    <xsd:element name="date_add" type="xsd:string"></xsd:element>
                    <xsd:element name="date_pick" type="xsd:string"></xsd:element>
                    <xsd:element name="prix" type="xsd:string"></xsd:element>
                    <xsd:element name="nom" type="xsd:string"></xsd:element>
                    <xsd:element name="gouvernerat" type="xsd:string"></xsd:element>
                    <xsd:element name="ville" type="xsd:string"></xsd:element>
                    <xsd:element name="adresse" type="xsd:string"></xsd:element>
                    <xsd:element name="tel" type="xsd:string"></xsd:element>
                    <xsd:element name="tel2" type="xsd:string"></xsd:element>
                    <xsd:element name="designation" type="xsd:string"></xsd:element>
                    <xsd:element name="nb_article" type="xsd:integer"></xsd:element>
                    <xsd:element name="msg" type="xsd:string"></xsd:element>
                    <xsd:element name="etat" type="xsd:integer"></xsd:element>
                    <xsd:element name="paye" type="xsd:integer"></xsd:element>
                    <xsd:element name="date_stat" type="xsd:string"></xsd:element>
                    <xsd:element name="agence_dest" type="xsd:integer"></xsd:element>
                    <xsd:element name="transmit" type="xsd:integer"></xsd:element>
                    <xsd:element name="recu" type="xsd:integer"></xsd:element>
                    <xsd:element name="id_recette" type="xsd:integer"></xsd:element>
                    <xsd:element name="unlink" type="xsd:integer"></xsd:element>
                    <xsd:element name="modif" type="xsd:integer"></xsd:element>
                    <xsd:element name="login" type="xsd:string"></xsd:element>
                    <xsd:element name="pwd" type="xsd:string"></xsd:element>
                    <xsd:element name="tracking_number" type="xsd:string"></xsd:element>
                    <xsd:element name="id_runsheet" type="xsd:string"></xsd:element>
                    <xsd:element name="longitude" type="xsd:string"></xsd:element>
                    <xsd:element name="latitude" type="xsd:string"></xsd:element>
                    <xsd:element name="motif" type="xsd:string"></xsd:element>
                    <xsd:element name="magasin" type="xsd:string"></xsd:element>
                    <xsd:element name="date_report" type="xsd:string"></xsd:element>
                    <xsd:element name="id_pickup" type="xsd:string"></xsd:element>
                    <xsd:element name="montant_espece" type="xsd:string"></xsd:element>
                    <xsd:element name="type_payment" type="xsd:string"></xsd:element>
                    <xsd:element name="list_mnt_cheque" type="xsd:string"></xsd:element>
                    <xsd:element name="list_num_cheque" type="xsd:string"></xsd:element>
                    <xsd:element name="nbr_colis_recu" type="xsd:string"></xsd:element>
                    <xsd:element name="id_motif" type="xsd:string"></xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="pickupTrackResponse">
                <xsd:all>
                    <xsd:element name="HasErrors" type="xsd:integer"></xsd:element>
                    <xsd:element name="ErrorsTxt" type="xsd:string"></xsd:element>
                    <xsd:element name="tracking_number" type="xsd:string"></xsd:element>
                    <xsd:element name="status" type="tns:statusTrack"></xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="statusTrack">
                <xsd:all>
                    <xsd:element name="etat" type="xsd:integer"></xsd:element>
                    <xsd:element name="dt" type="xsd:string"></xsd:element>
                    <xsd:element name="motif" type="xsd:string"></xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="pickupStatusUpdate">
                <xsd:all>
                    <xsd:element name="login" type="xsd:string"></xsd:element>
                    <xsd:element name="pwd" type="xsd:string"></xsd:element>
                    <xsd:element name="tracking_number" type="xsd:string"></xsd:element>
                    <xsd:element name="etat" type="xsd:string"></xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="livreur">
                <xsd:all>
                    <xsd:element name="login" type="xsd:string"></xsd:element>
                    <xsd:element name="pwd" type="xsd:string"></xsd:element>
                    <xsd:element name="runsheet" type="xsd:string"></xsd:element>
                    <xsd:element name="id_pickup" type="xsd:string"></xsd:element>
                    <xsd:element name="etat_pickup" type="xsd:string"></xsd:element>
		            <xsd:element name="nbr_colis" type="xsd:integer"></xsd:element>
		</xsd:all>
            </xsd:complexType>


            <xsd:complexType name="zone">
                <xsd:all>
                    <xsd:element name="login" type="xsd:string"></xsd:element>
                    <xsd:element name="pwd" type="xsd:string"></xsd:element>
                    <xsd:element name="zone_name" type="xsd:string"></xsd:element>
                    <xsd:element name="cad_colis" type="xsd:string"></xsd:element>
                    <xsd:element name="id_zone" type="xsd:string"></xsd:element>
                </xsd:all>
            </xsd:complexType>


            <xsd:complexType name="commande">
                <xsd:all>
                    <xsd:element name="id" type="xsd:string"></xsd:element>
                    <xsd:element name="date" type="xsd:string"></xsd:element>
                    <xsd:element name="colis" type="xsd:string"></xsd:element>
                    <xsd:element name="colis_livre" type="xsd:string"></xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="runsheet">
                <xsd:all>
                    <xsd:element name="HasErrors" type="xsd:integer"></xsd:element>
                    <xsd:element name="ErrorsTxt" type="xsd:string"></xsd:element>
                    <xsd:element name="commande" type="tns:commande"></xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="parcel">
                <xsd:all>
                    <xsd:element name="id" type="xsd:string"></xsd:element>
                    <xsd:element name="date" type="xsd:string"></xsd:element>
                    <xsd:element name="colis" type="xsd:string"></xsd:element>
                    <xsd:element name="colis_livre" type="xsd:string"></xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="ListParcel">
                <xsd:all>
                    <xsd:element name="HasErrors" type="xsd:integer"></xsd:element>
                    <xsd:element name="ErrorsTxt" type="xsd:string"></xsd:element>
                    <xsd:element name="parcel" type="tns:parcel"></xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="call">
                <xsd:all>
                    <xsd:element name="login" type="xsd:string"></xsd:element>
                    <xsd:element name="pwd" type="xsd:string"></xsd:element>
                    <xsd:element name="code_barre" type="xsd:string"></xsd:element>
                    <xsd:element name="id_runsheet" type="xsd:string"></xsd:element>
                    <xsd:element name="expediteur" type="xsd:string"></xsd:element>
                    <xsd:element name="file_name" type="xsd:string"></xsd:element>
		    <xsd:element name="date_call" type="xsd:string"></xsd:element>
                </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="cheque">
                <xsd:all>
                    <xsd:element name="login" type="xsd:string"></xsd:element>
                    <xsd:element name="pwd" type="xsd:string"></xsd:element>
                    <xsd:element name="code_barre" type="xsd:string"></xsd:element>
                    <xsd:element name="file_name" type="xsd:string"></xsd:element>
		        </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="typeCommande">
                <xsd:all>
                    <xsd:element name="login" type="xsd:string"></xsd:element>
                    <xsd:element name="pwd" type="xsd:string"></xsd:element>
                    <xsd:element name="tracking_number" type="xsd:string"></xsd:element>
                    <xsd:element name="list_type_cmd" type="xsd:string"></xsd:element>
		        </xsd:all>
            </xsd:complexType>
            <xsd:complexType name="new_runsheet">
                <xsd:all>
                    <xsd:element name="login" type="xsd:string"></xsd:element>
                    <xsd:element name="pwd" type="xsd:string"></xsd:element>
                    <xsd:element name="list_code" type="xsd:string"></xsd:element>
                    <xsd:element name="list_nbr_colis_recu" type="xsd:string"></xsd:element>
                    <xsd:element name="id_runsheet" type="xsd:string"></xsd:element>
                    <xsd:element name="matricule" type="xsd:string"></xsd:element>
                </xsd:all>
            </xsd:complexType>


            <xsd:complexType name="new_pickUp">
                <xsd:all>
                    <xsd:element name="login" type="xsd:string"></xsd:element>
                    <xsd:element name="pwd" type="xsd:string"></xsd:element>
                </xsd:all>
            </xsd:complexType>



        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="UpdateShipmentStatusRequest">
        <wsdl:part name="pickupStatusUpdate" type="tns:pickup"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="UpdateShipmentStatusResponse">
        <wsdl:part name="pickupTrackResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="ListRunsheetRequest">
        <wsdl:part name="livreur" type="tns:livreur"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="ListRunsheetResponse">
        <wsdl:part name="runsheet" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="ListParcelRunsheetRequest">
        <wsdl:part name="ListParcelRunsheetRequest" type="tns:livreur"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="ListParcelRunsheetResponse">
        <wsdl:part name="ListParcelRunsheetResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="LogCallRequest">
        <wsdl:part name="LogCallRequest" type="tns:call"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="LogCallResponse">
        <wsdl:part name="LogCallResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="ListPickUpRequest">
        <wsdl:part name="ListPickUpRequest" type="tns:livreur"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="ListPickUpResponse">
        <wsdl:part name="ListPickUpResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="UpdatePickupStatusRequest">
        <wsdl:part name="UpdatePickupStatusRequest" type="tns:livreur"></wsdl:part>
    </wsdl:message>

    <wsdl:message name="AddZoneLivreurColisRequest">
        <wsdl:part name="AddZoneLivreurColisRequest" type="tns:zone"></wsdl:part>
    </wsdl:message>

    <wsdl:message name="AddZoneLivreurRequest">
        <wsdl:part name="AddZoneLivreurRequest" type="tns:zone"></wsdl:part>
    </wsdl:message>

    <wsdl:message name="UpdatePickupStatusResponse">
        <wsdl:part name="UpdatePickupStatusResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>

    <wsdl:message name="AddZoneLivreurResponse">
        <wsdl:part name="AddZoneLivreurResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>

    <wsdl:message name="AddZoneLivreurColisResponse">
        <wsdl:part name="AddZoneLivreurColisResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>

    <wsdl:message name="TrackShipmentRequest">
        <wsdl:part name="TrackShipmentRequest" type="tns:pickup"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="TrackShipmentResponse">
        <wsdl:part name="TrackShipmentResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="AuthenticationRequest">
        <wsdl:part name="AuthenticationRequest" type="tns:pickup"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="AuthenticationResponse">
        <wsdl:part name="AuthenticationResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetSmsRequest">
        <wsdl:part name="GetSmsRequest" type="tns:livreur"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetSmsResponse">
        <wsdl:part name="GetSmsResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="InfoParcelRequest">
        <wsdl:part name="InfoParcelRequest" type="tns:pickup"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="InfoParcelResponse">
        <wsdl:part name="InfoParcelResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="AddRunsheetRequest">
        <wsdl:part name="AddRunsheetRequest" type="tns:new_runsheet"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="AddRunsheetResponse">
        <wsdl:part name="AddRunsheetResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="AddChequeRequest">
        <wsdl:part name="AddChequeRequest" type="tns:cheque"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="AddChequeResponse">
        <wsdl:part name="AddChequeResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="UpdateTypeCommandeRequest">
        <wsdl:part name="UpdateTypeCommandeRequest" type="tns:typeCommande"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="UpdateTypeCommandeResponse">
        <wsdl:part name="UpdateTypeCommandeResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetObservationRequest">
        <wsdl:part name="GetObservationRequest" type="tns:livreur"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="GetObservationResponse">
        <wsdl:part name="GetObservationResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="CreateNewPickupRequest">
        <wsdl:part name="CreateNewPickupRequest" type="tns:object"></wsdl:part>
    </wsdl:message>
    <wsdl:message name="CreateNewPickupResponse">
        <wsdl:part name="CreateNewPickupResponse" type="xsd:object"></wsdl:part>
    </wsdl:message>

    <wsdl:portType name="serviceDeliveryPortType">
        <wsdl:operation name="UpdateShipmentStatus">
            <wsdl:input message="tns:UpdateShipmentStatusRequest"/>
            <wsdl:output message="tns:UpdateShipmentStatusResponse"/>
        </wsdl:operation>
        <wsdl:operation name="ListRunsheet">
            <wsdl:input message="tns:ListRunsheetRequest"/>
            <wsdl:output message="tns:ListRunsheetResponse"/>
        </wsdl:operation>
        <wsdl:operation name="ListParcelRunsheet">
            <wsdl:input message="tns:ListParcelRunsheetRequest"/>
            <wsdl:output message="tns:ListParcelRunsheetResponse"/>
        </wsdl:operation>
        <wsdl:operation name="LogCall">
            <wsdl:input message="tns:LogCallRequest"/>
            <wsdl:output message="tns:LogCallResponse"/>
        </wsdl:operation>
        <wsdl:operation name="ListPickUp">
            <wsdl:input message="tns:ListPickUpRequest"/>
            <wsdl:output message="tns:ListPickUpResponse"/>
        </wsdl:operation>
        <wsdl:operation name="UpdatePickupStatus">
            <wsdl:input message="tns:UpdatePickupStatusRequest"/>
            <wsdl:output message="tns:UpdatePickupStatusResponse"/>
        </wsdl:operation>
        <wsdl:operation name="TrackShipment">
            <wsdl:input message="tns:TrackShipmentRequest"/>
            <wsdl:output message="tns:TrackShipmentResponse"/>
        </wsdl:operation>
        <wsdl:operation name="Authentication">
            <wsdl:input message="tns:AuthenticationRequest"/>
            <wsdl:output message="tns:AuthenticationResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetSms">
            <wsdl:input message="tns:GetSmsRequest"/>
            <wsdl:output message="tns:GetSmsResponse"/>
        </wsdl:operation>
        <wsdl:operation name="InfoParcel">
            <wsdl:input message="tns:InfoParcelRequest"/>
            <wsdl:output message="tns:InfoParcelResponse"/>
        </wsdl:operation>
        <wsdl:operation name="AddRunsheet">
            <wsdl:input message="tns:AddRunsheetRequest"/>
            <wsdl:output message="tns:AddRunsheetResponse"/>
        </wsdl:operation>
        <wsdl:operation name="AddCheque">
            <wsdl:input message="tns:AddChequeRequest"/>
            <wsdl:output message="tns:AddChequeResponse"/>
        </wsdl:operation>

        <wsdl:operation name="AddZone">
            <wsdl:input message="tns:AddZoneLivreurRequest"/>
            <wsdl:output message="tns:AddZoneLivreurResponse"/>
        </wsdl:operation>


        <wsdl:operation name="AddZoneLivColis">
            <wsdl:input message="tns:AddZoneLivreurColisRequest"/>
            <wsdl:output message="tns:AddZoneLivreurColisResponse"/>
        </wsdl:operation>

        <wsdl:operation name="UpdateTypeCommande">
            <wsdl:input message="tns:UpdateTypeCommandeRequest"/>
            <wsdl:output message="tns:UpdateTypeCommandeResponse"/>
        </wsdl:operation>
        <wsdl:operation name="GetObservation">
            <wsdl:input message="tns:GetObservationRequest"/>
            <wsdl:output message="tns:GetObservationResponse"/>
        </wsdl:operation>
        <wsdl:operation name="CreateNewPickup">
            <wsdl:input message="tns:CreateNewPickupRequest"/>
            <wsdl:output message="tns:CreateNewPickupResponse"/>
        </wsdl:operation>
    </wsdl:portType>

    <wsdl:binding name="serviceDeliveryBinding" type="tns:serviceDeliveryPortType">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="UpdateShipmentStatus">
            <soap:operation soapAction="serviceDelivery#UpdateShipmentStatusServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ListRunsheet">
            <soap:operation soapAction="serviceDelivery#ListRunsheetServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ListParcelRunsheet">
            <soap:operation soapAction="serviceDelivery#ListParcelRunsheetServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="LogCall">
            <soap:operation soapAction="serviceDelivery#LogCallServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="ListPickUp">
            <soap:operation soapAction="serviceDelivery#ListPickUpServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UpdatePickupStatus">
            <soap:operation soapAction="serviceDelivery#UpdatePickupStatusServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="TrackShipment">
            <soap:operation soapAction="serviceDelivery#TrackShipmentServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="Authentication">
            <soap:operation soapAction="serviceDelivery#AuthenticationServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetSms">
            <soap:operation soapAction="serviceDelivery#GetSmsServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="InfoParcel">
            <soap:operation soapAction="serviceDelivery#InfoParcelServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="AddRunsheet">
            <soap:operation soapAction="serviceDelivery#AddRunsheetServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="AddCheque">
            <soap:operation soapAction="serviceDelivery#AddChequeServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="UpdateTypeCommande">
            <soap:operation soapAction="serviceDelivery#UpdateTypeCommandeServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="GetObservation">
            <soap:operation soapAction="serviceDelivery#GetObservationServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="AddZone">
            <soap:operation soapAction="serviceDelivery#AddZoneServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="AddZoneLivColis">
            <soap:operation soapAction="serviceDelivery#AddZoneLivColisServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>

        <wsdl:operation name="CreateNewPickup">
            <soap:operation soapAction="serviceDelivery#CreateNewPickupServeur" style="document"/>
            <wsdl:input>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" namespace="serviceDelivery"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>

    <wsdl:service name="serviceDelivery">
        <wsdl:port binding="tns:serviceDeliveryBinding" name="serviceDeliveryPort">
            <soap:address location="https://adex.tn/api/serviceDelivery.php"/>
        </wsdl:port>
    </wsdl:service>

</wsdl:definitions>