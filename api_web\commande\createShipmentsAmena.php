<?php

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$gouvernorat_livraison ='';

// query to read single record
$query = "select * from commande  where id_cmd = $id_cmd";


// prepare query statement
$stmt = $this->conn->prepare( $query );



// execute query
$stmt->execute();

// get retrieved row
while($rescat = $stmt->fetch(PDO::FETCH_ASSOC)){
    if ($rescat['tel_cli2'] == '') {
        $tel2 = $rescat['tel_cli'];
    } else {
        $tel2 = $rescat['tel_cli2'];
    }
    $villeCli=$rescat['ville_cli'];
    switch ($villeCli) {
        case '1':
            $gouvernorat_livraison = 'Ariana';
            break;
        case '2':
            $gouvernorat_livraison = 'Beja';
            break;
        case '3':
            $gouvernorat_livraison = 'Ben arous';
            break;

        case '4':
            $gouvernorat_livraison = 'Bizerte';
            break;
        case '5':
            $gouvernorat_livraison = 'Gabes';
            break;

        case '6':
            $gouvernorat_livraison = 'Gafsa';
            break;

        case '7':
            $gouvernorat_livraison = 'Jendouba';
            break;
        case '8':
            $gouvernorat_livraison = 'Kairouan';
            break;
        case '9':
            $gouvernorat_livraison = 'Kasserine';
            break;

        case '10':
            $gouvernorat_livraison = 'Kebili';
            break;

        case '11':
            $gouvernorat_livraison = 'Le Kef';
            break;

        case '12':
            $gouvernorat_livraison = 'Mahdia';
            break;

        case '13':
            $gouvernorat_livraison = 'Mannouba';
            break;

        case '14':
            $gouvernorat_livraison = 'Medenine';
            break;

        case '15':
            $gouvernorat_livraison = 'Monastir';
            break;

        case '16':
            $gouvernorat_livraison = 'Nabeul';
            break;

        case '17':
            $gouvernorat_livraison = 'Sfax';
            break;
        case '18':
            $gouvernorat_livraison = 'Sidi bouzid';
            break;

        case '19':
            $gouvernorat_livraison = 'Siliana';
            break;

        case '20':
            $gouvernorat_livraison = 'Sousse';
            break;

        case '21':
            $gouvernorat_livraison = 'Tataouine';
            break;
        case '22':
            $gouvernorat_livraison = 'Touzeur';
            break;

        case '23':
            $gouvernorat_livraison = 'Tunis';
            break;

        case '24':
            $gouvernorat_livraison = 'Zaghouan';
            break;
    }
    $nomCli=$rescat['nom_cli'];
    $adrCli=$rescat['adr_cli'];
    $listProduitApi=$rescat['contenu_cmd'];

    $remarque=$rescat['observ_cmd'];
    $NumberOfPieces=$rescat['nbr_colis'];
    $echange=$rescat['echange_cmd'];
    $ttc_cmd=$rescat['ttc_cmd'];
    $id_exped=$rescat['id_exped'];
    $id_agence=$rescat['id_agence'];
    $id_agence_dest=$rescat['id_agence_dest'];







    $requestData = array(

        'user_id' => '165',
        'last statut' => '12',
        'client' => $nomCli,
        'cod' => $ttc_cmd,
        'adresse' => $adrCli,
        'governorat' => $gouvernorat_livraison,
        'gsm1' => $rescat['tel_cli'],
        'gsm2' => $rescat['tel_cli2'],
        'designation' => $listProduitApi,
        'nbre' => $NumberOfPieces,
        'remarque' => $remarque,
        'echange' => $echange
    );


    $curl_get_data = json_encode($requestData);
    $headers = array('Content-type: application/json');


    $url_api_create = 'http://transport.alamana-transport.tn/api/v1/colis/create?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJsdW1lbi1qd3QiLCJzdWIiOjE2NSwiaWF0IjoxNjY5NzMyNTYzLCJleHAiOjY2MzM5MTY2OTczMjU2M30.I6dWHVNpgkB-3JaVbDOpUD0ICYy_mCmYS4EpD4o1OtY';
    $curl = curl_init($url_api_create);
    curl_setopt($curl, CURLOPT_URL, $url_api_create);
    curl_setopt($curl, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
// curl_setopt($curl, CURLOPT_USERPWD, "$UserName:$Password");
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_POST, true);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $requestData);
    //curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    $curl_response = curl_exec($curl);
    $response = json_decode($curl_response);
    curl_close($curl);
    // var_dump($response);


   $num_suivi_cmd = $response->{'ean'};


    $reqCodeCmd="update commande set code_suivie_api='" . $num_suivi_cmd . "'     where id_cmd='" . $rescat['id_cmd'] ."'";
    $stmt_code_cmd = $this->conn->prepare($reqCodeCmd);
    $stmt_code_cmd->execute();


}
?>

