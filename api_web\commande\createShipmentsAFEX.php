<?php
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);


$gouvernorat_livraison ='';
// query to read single record
$query = "select * from commande  where id_cmd = $id_cmd";


// prepare query statement
$stmt = $this->conn->prepare( $query );



// execute query
$stmt->execute();

// get retrieved row
while($rescat = $stmt->fetch(PDO::FETCH_ASSOC)){
    if ($rescat['tel_cli2'] == '') {
        $tel2 = $rescat['tel_cli'];
    } else {
        $tel2 = $rescat['tel_cli2'];
    }
    $villeCli=$rescat['ville_cli'];
    switch ($villeCli) {
        case '1':
            $gouvernorat_livraison = 'Ariana';
            $delg = 'Ariana Ville';
            break;
        case '2':
            $gouvernorat_livraison = 'Beja';
            $delg = 'Beja Nord';
            break;
        case '3':
            $gouvernorat_livraison = 'Ben Arous';
            $delg = 'Ben Arous';
            break;

        case '4':
            $gouvernorat_livraison = 'Bizerte';
            $delg = 'Bizerte Nord';
            break;
        case '5':
            $gouvernorat_livraison = 'Gabes';
            $delg = 'Gabes Ouest';
            break;

        case '6':
            $gouvernorat_livraison = 'Gafsa';
            $delg = 'Gafsa Nord';
            break;

        case '7':
            $gouvernorat_livraison = 'Jendouba';
            $delg = 'Jendouba';
            break;
        case '8':
            $gouvernorat_livraison = 'Kairouan';
            $delg = 'Kairouan Nord';
            break;
        case '9':
            $gouvernorat_livraison = 'Kasserine';
            $delg = 'Kasserine Nord';
            break;

        case '10':
            $gouvernorat_livraison = 'Kebili';
            $delg = 'Kebili Nord';
            break;

        case '11':
            $gouvernorat_livraison = 'Kef';
            $delg = 'Le Kef Est';
            break;

        case '12':
            $gouvernorat_livraison = 'Mahdia';
            $delg = 'Mahdia';
            break;

        case '13':
            $gouvernorat_livraison = 'Mannouba';
            $delg = 'Mannouba';
            break;

        case '14':
            $gouvernorat_livraison = 'Medenine';
            $delg = 'Medenine Nord';
            break;

        case '15':
            $gouvernorat_livraison = 'Monastir';
            $delg = 'Monastir';
            break;

        case '16':
            $gouvernorat_livraison = 'Nabeul';
            $delg = 'Nabeul';
            break;

        case '17':
            $gouvernorat_livraison = 'Sfax';
            $delg = 'Sfax Ville';
            break;
        case '18':
            $gouvernorat_livraison = 'Sidi Bouzid';
            $delg = 'Sidi Bouzid Est';
            break;

        case '19':
            $gouvernorat_livraison = 'Siliana';
            $delg = 'Siliana Nord';
            break;

        case '20':
            $gouvernorat_livraison = 'Sousse';
            $delg = 'Sousse Ville'; 
            break;

        case '21':
            $gouvernorat_livraison = 'Tataouine';
            $delg = 'Tataouine Nord';
            break;
        case '22':
            $gouvernorat_livraison = 'Tozeur';
            $delg = 'Tozeur';
            break;

        case '23':
            $gouvernorat_livraison = 'Tunis';
            $delg = 'Tunis centre';
            break;

        case '24':
            $gouvernorat_livraison = 'Zaghouan';
            $delg = 'Zaghouan';
            break;
    }
    $nomCli=$rescat['nom_cli'];
    $adrCli=$rescat['adr_cli'];
    $code_barres_ext=$rescat['code_barres_cmd'];
    $listProduitApi=$rescat['contenu_cmd'];
    $echange=$rescat['echange_cmd'];
    $type_envoi_colis=1;
    if($echange==1){
        $type_envoi_colis=2;
    }
    $remarque=$rescat['observ_cmd'];
    $NumberOfPieces=$rescat['nbr_colis'];
    $echange=$rescat['echange_cmd'];
    $ttc_cmd=$rescat['ttc_cmd'];
    $id_exped=$rescat['id_exped'];
    $id_agence=$rescat['id_agence'];
    $id_agence_dest=$rescat['id_agence_dest'];


    if($echange!=0){
        $type_envoi_colis="Echange";

    }else{
        $type_envoi_colis="Livraison à domicile";
    }


    $tel_cli=str_replace(" ",'',$rescat['tel_cli']);
    if($rescat['tel_cli2']=='0'){
        $tel2='';
    }else{
        $tel2=$rescat['tel_cli2'];
        $tel2=str_replace(" ",'',$tel2);
    }

    $requestData = array(

        'nom' => $nomCli,
        'cod' => $ttc_cmd,
        'adresse' => $adrCli,
        'gouvernorat' => $gouvernorat_livraison,
        'delegation' => $delg,
        'telephone1' => $tel_cli,
        'telephone2' => $tel2,
        'marchandise' => $listProduitApi,
        'reference' => $code_barres_ext,
        'paquets' => $NumberOfPieces,
        'observation' => $remarque,
        'mode_reglement' => 'Seulement en espèces',
        'avec_decharge' => '1',
        'manifest' => '1',
        'manifest' => '',
        'type_envoi' => $type_envoi_colis
    );

    $curl_get_data = json_encode($requestData);

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://apis.afex.tn/v1/shipments',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_HTTPHEADER => array(
            'X-API-Key: b9d73027dacd4331f3218be973adbc09e8e10b3b',
            'Content-Type: application/text'
        ),
        CURLOPT_POSTFIELDS => $curl_get_data // 'input' refers to JSON Payload
    ));

    try {
        $response = curl_exec($curl);

        if ($response === false) {
            throw new Exception(curl_error($curl), curl_errno($curl));
        }

        //echo $response;

    } catch (Exception $ex) {
        echo $ex;
    }

    curl_close($curl);
    // $data = json_decode($response, true);
    $data = json_encode($response,true);

    $jsonString = str_replace("'", '"', $response);

    $data = json_decode($jsonString, true);
    $default='';
    $num_suivi_cmd = $data['barcode'];
    $agence_destination = $data['agence_destination'];
    $zone_destination = $data['zone_destination'];

    $reqCodeCmd="update commande set code_suivie_api2='" . $num_suivi_cmd . "'  , agence_destination_api2='" . $agence_destination . "'  , zone_destination_api2='" . $zone_destination . "'  , id_tr='1'  where id_cmd='" . $rescat['id_cmd'] ."'";
    $stmt_code_cmd = $this->conn->prepare($reqCodeCmd);
    $stmt_code_cmd->execute();



}
?>

