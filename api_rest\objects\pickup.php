<?php
include_once 'authentication.php';

class Pickup{

    // database connection and table name
    private $conn;
    private $auth;

    // constructor with $db as database connection
    public function __construct($db){
        $this->conn = $db;
        $this->auth = new Authentication($db);
    }

    // List pickups for livreur
    function list_pickup($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "pickup_list" => array()
            );
        }

        try {
            // Get agence of livreur
            $agence_id = $this->auth->get_agence_livreur($livreur_id);
            
            // Get today's pickups
            $today = date('Y-m-d');
            $stmt = $this->conn->prepare("SELECT * FROM pickup WHERE id_agence=:agence AND date_pick=:today AND etat_pickup=0 ORDER BY id_pickup DESC");
            $stmt->bindParam(':agence', $agence_id);
            $stmt->bindParam(':today', $today);
            $stmt->execute();

            $pickup_list = array();
            while($data = $stmt->fetch()) {
                $pickup_info = new stdClass();
                $pickup_info->code_barre = $data['code_barre'];
                $pickup_info->frs = $data['frs'];
                $pickup_info->id_frs = $data['id_frs'];
                $pickup_info->agence = $data['id_agence'];
                $pickup_info->date_add = $data['date_add'];
                $pickup_info->date_pick = $data['date_pick'];
                $pickup_info->prix = $data['prix'];
                $pickup_info->nom = $data['nom'];
                $pickup_info->gouvernerat = $data['gouvernerat'];
                $pickup_info->ville = $data['ville'];
                $pickup_info->adresse = $data['adresse'];
                $pickup_info->tel = $data['tel'];
                $pickup_info->tel2 = $data['tel2'];
                $pickup_info->id_pickup = $data['id_pickup'];
                $pickup_info->etat_pickup = $data['etat_pickup'];
                $pickup_info->nbr_colis = $data['nbr_colis'];
                $pickup_info->observation = $data['observation'];
                
                $pickup_list[] = $pickup_info;
            }

            $count = $stmt->rowCount();
            if($count < 1){
                return array(
                    "HasErrors" => 1,
                    "ErrorsTxt" => array("No pickups found for today"),
                    "pickup_list" => array()
                );
            }

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "pickup_list" => $pickup_list
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "pickup_list" => array()
            );
        }
    }

    // Update pickup status
    function update_pickup_status($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "success" => 0
            );
        }

        // Extract request data
        $id_pickup = isset($request_data['id_pickup']) ? $request_data['id_pickup'] : '';
        $etat = isset($request_data['etat']) ? $request_data['etat'] : '';
        $longitude = isset($request_data['longitude']) ? $request_data['longitude'] : '';
        $latitude = isset($request_data['latitude']) ? $request_data['latitude'] : '';
        $motif = isset($request_data['motif']) ? $request_data['motif'] : '';
        $date_report = isset($request_data['date_report']) ? $request_data['date_report'] : '';

        if(empty($id_pickup) || empty($etat)){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Missing required fields: id_pickup and etat"),
                "success" => 0
            );
        }

        try {
            $date_time = date('Y-m-d H:i:s');
            
            // Update pickup status
            $stmt = $this->conn->prepare("UPDATE pickup SET etat_pickup=:etat, date_modif=:date_time WHERE id_pickup=:id_pickup");
            $stmt->bindParam(':etat', $etat);
            $stmt->bindParam(':date_time', $date_time);
            $stmt->bindParam(':id_pickup', $id_pickup);
            $stmt->execute();

            // Log the pickup status change
            $stmt_log = $this->conn->prepare("INSERT INTO log_pickup (id_pickup, etat, date_log, id_livreur, longitude, latitude, motif, date_report) VALUES (:id_pickup, :etat, :date_time, :livreur_id, :longitude, :latitude, :motif, :date_report)");
            $stmt_log->bindParam(':id_pickup', $id_pickup);
            $stmt_log->bindParam(':etat', $etat);
            $stmt_log->bindParam(':date_time', $date_time);
            $stmt_log->bindParam(':livreur_id', $livreur_id);
            $stmt_log->bindParam(':longitude', $longitude);
            $stmt_log->bindParam(':latitude', $latitude);
            $stmt_log->bindParam(':motif', $motif);
            $stmt_log->bindParam(':date_report', $date_report);
            $stmt_log->execute();

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "success" => 1,
                "message" => "Pickup status updated successfully"
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "success" => 0
            );
        }
    }

    // Create new pickup
    function create_new_pickup($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "success" => 0
            );
        }

        // Extract request data
        $frs = isset($request_data['frs']) ? $request_data['frs'] : '';
        $id_frs = isset($request_data['id_frs']) ? $request_data['id_frs'] : '';
        $nom = isset($request_data['nom']) ? $request_data['nom'] : '';
        $gouvernerat = isset($request_data['gouvernerat']) ? $request_data['gouvernerat'] : '';
        $ville = isset($request_data['ville']) ? $request_data['ville'] : '';
        $adresse = isset($request_data['adresse']) ? $request_data['adresse'] : '';
        $tel = isset($request_data['tel']) ? $request_data['tel'] : '';
        $tel2 = isset($request_data['tel2']) ? $request_data['tel2'] : '';
        $prix = isset($request_data['prix']) ? $request_data['prix'] : '';
        $nbr_colis = isset($request_data['nbr_colis']) ? $request_data['nbr_colis'] : '';
        $observation = isset($request_data['observation']) ? $request_data['observation'] : '';
        $date_pick = isset($request_data['date_pick']) ? $request_data['date_pick'] : date('Y-m-d');

        // Validate required fields
        if(empty($nom) || empty($tel) || empty($adresse)){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Missing required fields: nom, tel, adresse"),
                "success" => 0
            );
        }

        try {
            $agence_id = $this->auth->get_agence_livreur($livreur_id);
            $date_add = date('Y-m-d H:i:s');
            $code_barre = $this->generate_pickup_code();

            // Insert new pickup
            $stmt = $this->conn->prepare("INSERT INTO pickup (code_barre, frs, id_frs, id_agence, date_add, date_pick, prix, nom, gouvernerat, ville, adresse, tel, tel2, nbr_colis, observation, etat_pickup) VALUES (:code_barre, :frs, :id_frs, :agence, :date_add, :date_pick, :prix, :nom, :gouvernerat, :ville, :adresse, :tel, :tel2, :nbr_colis, :observation, 0)");
            
            $stmt->bindParam(':code_barre', $code_barre);
            $stmt->bindParam(':frs', $frs);
            $stmt->bindParam(':id_frs', $id_frs);
            $stmt->bindParam(':agence', $agence_id);
            $stmt->bindParam(':date_add', $date_add);
            $stmt->bindParam(':date_pick', $date_pick);
            $stmt->bindParam(':prix', $prix);
            $stmt->bindParam(':nom', $nom);
            $stmt->bindParam(':gouvernerat', $gouvernerat);
            $stmt->bindParam(':ville', $ville);
            $stmt->bindParam(':adresse', $adresse);
            $stmt->bindParam(':tel', $tel);
            $stmt->bindParam(':tel2', $tel2);
            $stmt->bindParam(':nbr_colis', $nbr_colis);
            $stmt->bindParam(':observation', $observation);
            
            $stmt->execute();
            $pickup_id = $this->conn->lastInsertId();

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "success" => 1,
                "pickup_id" => $pickup_id,
                "code_barre" => $code_barre,
                "message" => "Pickup created successfully"
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "success" => 0
            );
        }
    }

    // Generate pickup code
    private function generate_pickup_code(){
        return date('ymdHis') . rand(100, 999);
    }
}
?>
