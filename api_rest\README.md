# ServiceDelivery REST API

This REST API is a conversion of the original SOAP-based ServiceDelivery API to modern REST endpoints using JSON for request/response data.

## Base URL
```
https://your-domain.com/api_rest/
```

## Authentication
All endpoints require authentication using `login` and `pwd` parameters in the request body.

## Common Response Format
All endpoints return JSON responses with the following structure:
```json
{
    "HasErrors": 0|1,
    "ErrorsTxt": ["error messages"],
    "success": 0|1,
    "data": {...}
}
```

## Endpoints

### Authentication

#### POST /authentication/authenticate.php
Authenticate a delivery person and get their information.

**Request:**
```json
{
    "login": "username",
    "pwd": "password"
}
```

**Response:**
```json
{
    "HasErrors": 0,
    "ErrorsTxt": [],
    "success": 1,
    "livreur_info": {
        "id_livreur": "123",
        "nom_livreur": "<PERSON>",
        "login_livreur": "username",
        "id_agence": "1"
    },
    "zones": [
        {
            "id_zone": "1",
            "nom_zone": "Zone A"
        }
    ]
}
```

### Delivery Operations

#### POST /delivery/update_shipment_status.php
Update the status of a shipment.

**Request:**
```json
{
    "login": "username",
    "pwd": "password",
    "tracking_number": "*********",
    "etat": "2",
    "id_runsheet": "456",
    "longitude": "10.123456",
    "latitude": "36.123456",
    "motif": "Delivered successfully",
    "montant_espece": "50.00",
    "type_payment": "0"
}
```

#### POST /delivery/track_shipment.php
Track a shipment and get its history.

**Request:**
```json
{
    "login": "username",
    "pwd": "password",
    "tracking_number": "*********"
}
```

**Response:**
```json
{
    "HasErrors": 0,
    "ErrorsTxt": [],
    "commande_info": {
        "code_barres_cmd": "*********",
        "nom_cli": "Customer Name",
        "adr_cli": "Customer Address",
        "ttc_cmd": "100.00"
    },
    "tracking_info": [
        {
            "etat": "1",
            "date_log": "2023-01-01 10:00:00",
            "description": "Package picked up"
        }
    ]
}
```

#### POST /delivery/info_parcel.php
Get detailed information about a parcel.

**Request:**
```json
{
    "login": "username",
    "pwd": "password",
    "tracking_number": "*********"
}
```

### Pickup Operations

#### POST /pickup/list_pickup.php
Get list of pickups for today.

**Request:**
```json
{
    "login": "username",
    "pwd": "password"
}
```

**Response:**
```json
{
    "HasErrors": 0,
    "ErrorsTxt": [],
    "pickup_list": [
        {
            "id_pickup": "123",
            "code_barre": "PU123456",
            "nom": "Customer Name",
            "adresse": "Customer Address",
            "tel": "12345678",
            "prix": "25.00",
            "nbr_colis": "2"
        }
    ]
}
```

#### POST /pickup/update_pickup_status.php
Update the status of a pickup.

**Request:**
```json
{
    "login": "username",
    "pwd": "password",
    "id_pickup": "123",
    "etat": "1",
    "longitude": "10.123456",
    "latitude": "36.123456",
    "motif": "Pickup completed"
}
```

#### POST /pickup/create_new_pickup.php
Create a new pickup request.

**Request:**
```json
{
    "login": "username",
    "pwd": "password",
    "nom": "Customer Name",
    "tel": "12345678",
    "adresse": "Customer Address",
    "ville": "City",
    "gouvernerat": "State",
    "prix": "25.00",
    "nbr_colis": "2",
    "observation": "Special instructions"
}
```

### Runsheet Operations

#### POST /runsheet/list_runsheet.php
Get list of active runsheets for the delivery person.

**Request:**
```json
{
    "login": "username",
    "pwd": "password"
}
```

**Response:**
```json
{
    "HasErrors": 0,
    "ErrorsTxt": [],
    "runsheet_list": [
        {
            "id": "456",
            "date": "2023-01-01",
            "colis": "10",
            "colis_livre": "5"
        }
    ]
}
```

#### POST /runsheet/list_parcel_runsheet.php
Get list of parcels in a specific runsheet.

**Request:**
```json
{
    "login": "username",
    "pwd": "password",
    "id_runsheet": "456"
}
```

#### POST /runsheet/add_runsheet.php
Create a new runsheet.

**Request:**
```json
{
    "login": "username",
    "pwd": "password",
    "matricule": "MAT123",
    "date_runsheet": "2023-01-01",
    "observation": "Notes",
    "parcels": ["*********", "987654321"]
}
```

### Utility Operations

#### POST /utils/get_sms.php
Get SMS messages for the delivery person.

**Request:**
```json
{
    "login": "username",
    "pwd": "password"
}
```

#### POST /utils/get_observation.php
Get list of available observations.

**Request:**
```json
{
    "login": "username",
    "pwd": "password"
}
```

#### POST /utils/log_call.php
Log a phone call.

**Request:**
```json
{
    "login": "username",
    "pwd": "password",
    "code_barre": "*********",
    "id_runsheet": "456",
    "expediteur": "0",
    "file_name": "call_recording.amr"
}
```

#### POST /utils/add_cheque.php
Add a cheque payment.

**Request:**
```json
{
    "login": "username",
    "pwd": "password",
    "code_barre": "*********",
    "num_cheque": "CHQ123",
    "montant_cheque": "100.00",
    "banque": "Bank Name"
}
```

#### POST /utils/update_type_commande.php
Update the type of a command.

**Request:**
```json
{
    "login": "username",
    "pwd": "password",
    "code_barre": "*********",
    "type_commande": "express"
}
```

## Error Handling

All endpoints return appropriate HTTP status codes and error messages in the response body:

- `200 OK`: Successful request
- `400 Bad Request`: Missing required parameters
- `401 Unauthorized`: Authentication failed
- `500 Internal Server Error`: Database or server error

## Migration from SOAP

This REST API provides the same functionality as the original SOAP API with the following mappings:

| SOAP Function | REST Endpoint |
|---------------|---------------|
| Authentication | POST /authentication/authenticate.php |
| UpdateShipmentStatus | POST /delivery/update_shipment_status.php |
| TrackShipment | POST /delivery/track_shipment.php |
| InfoParcel | POST /delivery/info_parcel.php |
| ListPickUp | POST /pickup/list_pickup.php |
| UpdatePickupStatus | POST /pickup/update_pickup_status.php |
| CreateNewPickup | POST /pickup/create_new_pickup.php |
| ListRunsheet | POST /runsheet/list_runsheet.php |
| ListParcelRunsheet | POST /runsheet/list_parcel_runsheet.php |
| AddRunsheet | POST /runsheet/add_runsheet.php |
| GetSms | POST /utils/get_sms.php |
| GetObservation | POST /utils/get_observation.php |
| LogCall | POST /utils/log_call.php |
| AddCheque | POST /utils/add_cheque.php |
| UpdateTypeCommande | POST /utils/update_type_commande.php |
