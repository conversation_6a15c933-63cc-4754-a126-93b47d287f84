<?php
// Get JSON input
$entityBody = json_decode(file_get_contents('php://input'), true);

// Required headers
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Include database and object files
include_once '../config/database.php';
include_once '../objects/utils.php';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Initialize utils object
$utils = new Utils($db);

// Validate input
if(!isset($entityBody['login']) || !isset($entityBody['pwd'])){
    echo json_encode(array(
        "HasErrors" => 1,
        "ErrorsTxt" => array("Missing login or password"),
        "observation_list" => array()
    ));
    exit;
}

// Get observations
$result = $utils->get_observation($entityBody);

// Return response
echo json_encode($result);
?>
