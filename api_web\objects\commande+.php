<?php
include_once '../objects/authentification.php';


class Commande{

    // database connection and table name
    private $conn;

    // constructor with $db as database connection
    public function __construct($db){
        $this->authentification =  new authentification($db);
         $verif_auth=$this->authentification->authentification_user();
         if($verif_auth==0){
             exit;
        }
        
        $this->conn = $db;

    }
    
    //insertion colis
    function add_colis(){
        $this->date_cmd=time();
        $this->id_exp=$this->authentification->id_exp;
        
         $this->sqlTest="insert into commande (
                id_client,
                nom_cli,
                adr_cli,
                ville_cli,
                tel_cli,
                tel_cli2,
                ttc_cmd,
                nbr_colis,
                id_exped,
                id_agence,
                id_agence_origine,
                id_agence_dest,
                date_cmd,
                observ_cmd, 
                code_barres_ext,
                contenu_cmd,
                echange_cmd,
                fragile,
                echange_ancienne_cmd
             )values(
                '0',      
                '" . $this->nom_cli . "',      
                '" . $this->adr_cli . "',
                '" . $this->ville_cli . "', 
                '" . $this->tel_cli . "',
                '" . $this->tel_cli2 . "',
                '" . $this->ttc_cmd . "',
                '" . $this->nbr_colis . "' ,
                '" . $this->id_exp . "' ,
                '0' ,
                '0' ,
                '0' ,
                '" . $this->date_cmd . "' ,
                '" . $this->commentaire_cmd . "' , 
                '" . $this->code_barres_ext . "' ,
                '" . $this->ContenuColis . "' ,
                '" . $this->echange_cmd . "' ,
                '" . $this->fragile . "' ,
                '" . $this->ancienne_commande . "' 
             )";
        
        
        
        //insertion clent
        $query_insert_client = "insert into commande_client (
                nom_cli,
                adr_cli,
                ville_cli,
                tel_cli,
                tel_cli2,
                tel_cli3,
                id_exped
                )values(
                '" . $this->nom_cli . "',      
                '" . $this->adr_cli . "',      
                '" . $this->ville_cli . "',      
                '" . $this->tel_cli . "',      
                '" . $this->tel_cli2 . "',      
                '" . $this->tel_cli3 . "',      
                '" . $this->id_exp . "'     
             )";
        $stmt_insert_client = $this->conn->prepare($query_insert_client);
        $stmt_insert_client->execute();
        $this->id_cli = $this->conn->lastInsertId();  
        //fin insertion client
           
        $this->id_agence=$this->authentification->id_agence;
       // $this->id_agence=1;
        $this->id_agence_dest=$this->get_agDest($this->ville_cli);
        
        //insertion commande
        $query_insert = "insert into commande (
                id_client,
                nom_cli,
                adr_cli,
                ville_cli,
                tel_cli,
                tel_cli2,
                ttc_cmd,
                nbr_colis,
                id_exped,
                id_agence,
                id_agence_origine,
                id_agence_dest,
                date_cmd,
                observ_cmd, 
                code_barres_ext,
                contenu_cmd,
                echange_cmd,
                fragile,
                echange_ancienne_cmd
             )values(
                '" . $this->id_cli . "',      
                '" . $this->nom_cli . "',      
                '" . $this->adr_cli . "',
                '" . $this->ville_cli . "', 
                '" . $this->tel_cli . "',
                '" . $this->tel_cli2 . "',
                '" . $this->ttc_cmd . "',
                '" . $this->nbr_colis . "' ,
                '" . $this->id_exp . "' ,
                '" . $this->id_agence . "' ,
                '" . $this->id_agence . "' ,
                '" . $this->id_agence_dest . "' ,
                '" . $this->date_cmd . "' ,
                '" . $this->commentaire_cmd . "' , 
                '" . $this->code_barres_ext . "' ,
                '" . $this->ContenuColis . "' ,
                '" . $this->echange_cmd . "' ,
                '" . $this->fragile . "' ,
                '" . $this->ancienne_commande . "' 
             )";
        
       
        $stmt_insert = $this->conn->prepare($query_insert);
        $stmt_insert->execute();
        $this->id_cmd = $this->conn->lastInsertId();
        //fin insertion commande
           
        //génération du code barres format-> AAMMJJid_expedId_commande
        $this->code_barres_cmd = date('ymd', time()).str_pad($this->id_cmd, 8, '0', STR_PAD_LEFT);
        $reqCodeCmd="update commande set   code_barres_cmd='".$this->code_barres_cmd."'  where id_cmd='".$this->id_cmd."'";
        $stmt_code_cmd = $this->conn->prepare($reqCodeCmd);
        $stmt_code_cmd->execute();
        
        
        //insertion colis d'échange
            //insertion du produit a échangé
            if($this->echange_cmd=='1') {
                $this->code_barres_produit = '00'.date('ymd', time()).str_pad($this->id_cmd, 5, '0', STR_PAD_LEFT);

                $reqInsertEch ="insert into produit_echange (
                            nom_produit,
                            id_cmd,
                            id_exp,
                            date_produit,
                            etat_produit,
                            agence_produit,
                            agence_origine_produit,
                            agence_destination_produit,
                            code_barres_produit
                         )values(
                            '" . $this->produit_arecevoir . "',
                            '" . $this->id_cmd . "',
                            '" . $this->id_exp . "',
                            '" . $this->date_cmd . "',
                            '0',
                            '" . $this->id_agence . "',
                            '" . $this->id_agence . "',
                            '" . $this->id_agence_dest . "',
                            '" .  $this->code_barres_produit . "'
                         )";
                $stmt_insertEch = $this->conn->prepare($reqInsertEch);
                $stmt_insertEch->execute();
            }
        //fin insertion colis d'échange
        
        //insertion des piéces
            $tabTaille=array();
            $tabTaille=explode('/',$this->type_colis_tab) ;
            for ($z = 0; $z < $this->nbr_colis; $z++) {
                $type_colis = intval($tabTaille[$z]);
                $reqInsertEch = "INSERT INTO commande_ligne(
                    id_cmd,
                    taille
                ) VALUES ( 
                    $this->id_cmd,
                    $type_colis
                    )";
                $stmt_insertEch = $this->conn->prepare($reqInsertEch);
                $stmt_insertEch->execute();
            }
        //fin insertion piéce
        
        
        
        
        //insertion log creation commande

                $reqInsertLog ="insert into commande_log (
                            id_cmd,
                            id_exp,
                            id_agent,
                            type_agent,
                            action_log,
                            type_action,
                            date_log,
                            id_agence,
                            id_agence_dest
                         )values(
                            '" . $this->id_cmd . "',
                            '" . $this->id_exp . "',
                            '" . $this->id_exp . "',
                            '0',
                            '0',
                            '0',
                            '" . $this->date_cmd . "',
                            '" . $this->id_exp . "',
                            '" . $this->id_agence_dest . "'
                         )";
                $stmt_insertLog = $this->conn->prepare($reqInsertLog);
                $stmt_insertLog->execute();
        //fin insertion log creation commande


        //////////////////////////////////////Amena API
       //if($this->ville_cli=='14' or $this->ville_cli=='6' or $this->ville_cli=='18' or $this->ville_cli=='9' or $this->ville_cli=='22' or $this->ville_cli=='10' or $this->ville_cli=='5' or $this->ville_cli=='21' ){

            //$dossier_api_tr='amena';
            //$id_cmd=$this->id_cmd;
            //  echo '../../includes/api/'.$dossier_api_tr.'/createShipment/createShipments.php';
           //include('../commande/createShipmentsAmena.php');

        //}



  ////////////////////////////////////// AFEX tr
       if($this->ville_cli=='14' or $this->ville_cli=='6' or $this->ville_cli=='18' or $this->ville_cli=='9' or $this->ville_cli=='22' or $this->ville_cli=='10' or $this->ville_cli=='5' or $this->ville_cli=='21' or $this->ville_cli=='7' or $this->ville_cli=='2' or $this->ville_cli=='19' or $this->ville_cli=='11'  ){

            $dossier_api_tr='AFEX';

            $id_cmd=$this->id_cmd;
           include('../commande/createShipmentsAFEX.php');

        }



        ////////////////////////////////////// Jola tr
        if($this->ville_cli=='8' ){// kairouan

            $dossier_api_tr='jola';

            $id_cmd=$this->id_cmd;
            //include('../commande/createShipmentsJola.php');

        }


      
           
        if($this->id_cmd!=null){
            return true;
        }

           return false;       
    }
    
       // get the details of commande by num_suivi_cmd
    function get_etat_cmd(){
        $this->etat_cmd_arr=array();
        // query to read single record
        //30 enregistrement max
        $array_ = explode(",", $this->num_suivi_cmd);
        $array_ = array_slice($array_, 0, 100);
        $string_ = implode(",", $array_);

        $query = "select * from commande where code_barres_cmd in( $string_)";
        /*if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        if($ip=='**************'){
            file_put_contents('test.txt', var_export(   $query, TRUE));
        }*/


        // prepare query statement
        $stmt = $this->conn->prepare( $query );

        // bind id of product to be updated
        $stmt->bindParam(1, $string_);

        // execute query
        $stmt->execute();

        // get retrieved row
        while($row = $stmt->fetch(PDO::FETCH_ASSOC)){

            // set values to object properties
            $this->code_cmd = $row['code_barres_cmd'];
            $this->id_cmd = $row['id_cmd'];
            $this->etat_retour = $row['etat_retour'];
            $this->etat_cmd = $row['etat_cmd'];
            $this->etat_paye = $row['etat_paye_exped'];
            $this->date_livraison_retour = $row['date_livraison_retour'];
            $this->etat_validation = $row['etat_validation'];


            $this->lib_etat_cmd='';
            $this->lib_etat_paye='';
            $this->lib_etat_retour='';
                switch ($this->etat_cmd) {
                    case 0:
                        $this->lib_etat_cmd= "En attente";
                        break;
                    case 1:
                        $this->lib_etat_cmd= "Ramassé";
                        break;
                    case 2:
                        $this->lib_etat_cmd= "Au dépôt";
                        break;
                    case 3:
                        $this->lib_etat_cmd= "En livraison";
                        break;
                    case 4:
                        $this->lib_etat_cmd= "Reporté";
                        break;
                    case 5:
                        $this->lib_etat_cmd= "Livré";
                        break;
                    case 6:
                        $this->lib_etat_cmd= "Retour";
                        break;
                }
              if($this->etat_cmd=='5'){
                  switch ($this->etat_paye) {
                    case 0:
                        $this->lib_etat_paye= " non payé";
                        break;
                    case 1:
                        $this->lib_etat_paye= " payé";
                        break;
                  }
              }
            if($this->etat_cmd=='6'){
                  switch ($this->etat_retour) {
                    case 0:
                        $this->lib_etat_retour= " définitif";
                        break;
                    case 1:
                        $this->lib_etat_retour= " agence origine";
                        break;
                    case 2:
                        $this->lib_etat_retour= " expéditeur";
                        break;
                  }
                if($this->etat_validation==0){
                    $this->lib_etat_retour.= " en attente de validation";
                }else{
                    $this->lib_etat_retour.= " validé";
                }
              }
            $this->lib_etat=$this->lib_etat_cmd.$this->lib_etat_paye.$this->lib_etat_retour;
             $this->motif_retour='';
            if(($this->etat_cmd=='6')or($this->etat_cmd=='4')){
                $reqmotif=("SELECT * FROM `commande_log` WHERE id_cmd ='" . $this->id_cmd . "' and type_action=0 and (action_log=4 or action_log=6)");
                $stmtReqmotif = $this->conn->prepare($reqmotif);
                $stmtReqmotif->execute();
                $resMotif = $stmtReqmotif->fetch(PDO::FETCH_ASSOC);
                $this->motif_retour=$resMotif['observation'];
            }

             array_push($this->etat_cmd_arr,array(
                        "code_barres_cmd" => $this->code_cmd,
                        "etat_cmd" => $this->lib_etat,
                        "code_etat_cmd" => $this->etat_cmd,
                        "etat_validation_retour" => $this->etat_validation,
                        "date_livraison_retour" => $this->date_livraison_retour,
                        "motif" => $this->motif_retour,
                        "etat_paye" => $this->etat_paye,
                        "etat_retour" => $this->etat_retour
                        ));


        }
        if(count($row)>0){
            return true;
        }
        return false;
    }
 
 

      function tracking_cmd(){
       $this->log_arr=array();
        $this->id_cmd=$this->getInfoCmd($this->num_suivi_cmd,'id_cmd');
        $this->etat_cmd=$this->getInfoCmd($this->num_suivi_cmd,'etat_cmd');
        $this->id_exped=$this->getInfoCmd($this->num_suivi_cmd,'id_exped');
        $this->id_agence_cmd=$this->getInfoCmd($this->num_suivi_cmd,'id_agence');
        $nom_agence_cmd=$this->getInfoAgence($this->id_agence_cmd,'nom_agence');
        // query to read single record
        $query = "select * from commande_log where id_cmd = ?";

        // prepare query statement
        $stmt = $this->conn->prepare( $query );

        // bind id of product to be updated
        $stmt->bindParam(1,$this->id_cmd);

        // execute query
        $stmt->execute();
        // get retrieved row
        while($row = $stmt->fetch(PDO::FETCH_ASSOC)){
            $this->idlog=$row['id_log'];
            $type_action=$row['type_action'];
            $action_log=$row['action_log'];
            $id_agent=$row['id_agent'];
            $id_agence_inter=$row['id_agence'];
            $id_agence_inter_dest=$row['id_agence_dest'];
            $type_agent=$row['type_agent'];
            $date_log=date('d-m-Y H:i',$row['date_log']);
            $heure_log=date('H:i',$row['date_log']);
            $type_agent=$row['type_agent'];
            $nom_agent=$this->get_nom_utlisateur_log($id_agent,$type_agent);

            if($type_action=='0'){ //log_etat_commande 
                    switch ($action_log) {
                        case 0:
                                $description= "Créé par ".$nom_agent;
                                break; 
                        case 1:
                                $description= "Ramassé par ".$nom_agent.' vers '.$nom_agence_cmd;
                                break; 
                        case 2:
                                $description= "Reçu par ".$nom_agent ;
                                break; 

                        case 3:
                                $description= "Sortie du ".$nom_agent;
                                break;  
                        case 4:
                                $description= "Reporté";
                                break;
                        case 5:
                                $description= "Livré Par ".$nom_agent;
                                break;
                         case 6:
                                $description= "Retour Par ".$nom_agent;
                                break; 
                    }
                }else if($type_action=='1'){ //log_etat_retour
                     switch ($action_log) {
                         case 0:
                                $description= "Retour au dépôt par =".$nom_agent;
                                break; 
                         case 1:
                                $description= "Reçue par ".$nom_agent;                                     
                                break; 
                         case 2:
                                 $description= "Reçue par ".$this->getInfVendeur($this->id_exped,'nom_exp');
                                break; 

                    }
                }else if($type_action=='2'){ //log_etat_paiement
                     if($this->etat_cmd==5){
                     switch ($action_log) {
                            case 0:
                                $description= "Paiement";                                   
                                break; 
                            case 1:
                                $description= "Préparation paiement";                                   
                                break; 
                            case 2:
                                $description= "Paiement validé";                                   
                                break; 
                        }
                        
                     }else{
                          switch ($action_log) {
                          case 0:
                                $description= "Réglement";                                   
                                break; 
                            case 1:
                                $description= "Préparation frais de livraison";                                   
                                break; 
                            case 2:
                                $description= "Facture validée ";                                   
                                break; 
                          }
                         
                     }
                }else if($type_action=='3'){ //inetr_depot
                    switch ($action_log) {
                        case 0:
                            $description= "Transmit de ".$this->getInfoAgence($id_agence_inter,'nom_agence').' à '.$this->getInfoAgence($id_agence_inter_dest,'nom_agence');
                            break;
                        case 1:
                            $description= "Accepté par ".$this->getInfoAgence($id_agence_inter_dest,'nom_agence').' envoyé par '.$this->getInfoAgence($id_agence_inter,'nom_agence');
                            break;


                    }
                }
              array_push($this->log_arr,array(
                        "date_log" => $date_log,
                        "description" => $description
                        ));
        }
  
        if(count($row)>0){
            return true;
        }
        return false;
    }
     function get_agDest($villeCli){
        $reqAd=("select * from ville_agence WHERE id_ville=".$villeCli);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $id_agence_dest=$resAd['id_agence'];
    }   

    function getInfoAgence($id,$type){
        $reqAd=("select * from agence WHERE id_agence=".$id);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $resAd[$type];
    }  

    function getInfoLivreur($id,$type){
        $reqAd=("select * from livreur WHERE id_livreur=".$id);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $resAd[$type];
    } 
    

    function getInfoAgent_agence($id,$type){
        $reqAd=("select * from agent WHERE id_agent=".$id);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $resAd[$type];
    } 
    

    function getInfVendeur($id,$type){
        $reqAd=("select * from expediteur WHERE id_exp=".$id);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $resAd[$type];
    } 
     function getInfoCmd($code,$type){
        $reqAd=("select * from commande WHERE code_barres_cmd=".$code);
        $stmtReqAd = $this->conn->prepare($reqAd);
        $stmtReqAd->execute();
        $resAd = $stmtReqAd->fetch(PDO::FETCH_ASSOC);
        return $resAd[$type];
    } 
    
    function get_nom_utlisateur_log($id_agent,$type_agent){
       switch ($type_agent) { //get_nom_utlisateur_log
            case 0:
                $nom_agent= $this->getInfVendeur($id_agent,'nom_exp');
                break; 
            case 1:
                $id_agence=$this->getInfoAgent_agence($id_agent,'id_agence');
                $nom_agent= $this->getInfoAgence($id_agence,'nom_agence').' ('. $this->getInfoAgent_agence($id_agent,'nom_agent').')';
                break; 
            case 2:
                $nom_agent= $this->getInfoLivreur($id_agent,'nom_livreur');
                break; 
        }
        return $nom_agent;
    }
    function encryptIt($string)
    {
        $output = false;

        $encrypt_method = "AES-256-CBC";
        $secret_key = 'This is my secret key';
        $secret_iv = 'This is my secret iv';

        // hash
        $key = hash('sha256', $secret_key);

        // iv - encrypt method AES-256-CBC expects 16 bytes - else you will get a
        // warning
        $iv = substr(hash('sha256', $secret_iv), 0, 16);

        $output = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
        $output = base64_encode($output);

        return $output;
    }
    
}
function clean_chaine($str){
    $caractere = array("'",".", '"');
    $remplace = array(" "," ", " ");
    $str = str_replace($caractere, $remplace, $str);
    return trim($str);
}

