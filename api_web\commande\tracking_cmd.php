<?php
$entityBody = json_decode(file_get_contents('php://input'),true);

header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// include database and object files
include_once '../config/database.php';
include_once '../objects/commande.php';

 //get database connection
$database = new Database();
$db = $database->getConnection();

//// prepare product object
$commande = new commande($db);
//

$commande->num_suivi_cmd = urldecode($entityBody['num_suivi_cmd']) ;


if($commande->tracking_cmd()){
    
   echo json_encode($commande->log_arr);
}else{
    echo json_encode(array("message" => "ERROR.","success"=>0));
}

?>