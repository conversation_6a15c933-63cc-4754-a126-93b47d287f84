<?php
// required headers
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

// include database and object files
include_once '../config/database.php';
include_once '../objects/authentification.php';

// instantiate database and product object
$database = new Database();
$db = $database->getConnection();

// initialize object
$authentification = new authentification($db);

// query products
    $result= $authentification->authentification_user();
if($result==1){
   echo json_encode(array("success"=>1,"info_user"=>$authentification->arr_info_user));
 
}else{
echo json_encode(array("success"=>0,"info_user"=>$authentification->arr_info_user));
}


