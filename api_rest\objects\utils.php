<?php
include_once 'authentication.php';

class Utils{

    // database connection and table name
    private $conn;
    private $auth;

    // constructor with $db as database connection
    public function __construct($db){
        $this->conn = $db;
        $this->auth = new Authentication($db);
    }

    // Get SMS messages for livreur
    function get_sms($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "sms_list" => array()
            );
        }

        try {
            // Get SMS messages
            $stmt = $this->conn->prepare("SELECT * FROM sms WHERE id_livreur=:livreur_id AND etat_sms=0 ORDER BY date_sms DESC");
            $stmt->bindParam(':livreur_id', $livreur_id);
            $stmt->execute();

            $sms_list = array();
            while($data = $stmt->fetch()) {
                $sms_info = new stdClass();
                $sms_info->id_sms = $data['id_sms'];
                $sms_info->message = $data['message_sms'];
                $sms_info->date_sms = $data['date_sms'];
                $sms_info->expediteur = $data['expediteur'];
                $sms_info->etat_sms = $data['etat_sms'];
                
                $sms_list[] = $sms_info;
            }

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "sms_list" => $sms_list
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "sms_list" => array()
            );
        }
    }

    // Get observations
    function get_observation($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "observation_list" => array()
            );
        }

        try {
            // Get observations
            $stmt = $this->conn->prepare("SELECT * FROM observation WHERE etat_observation=1 ORDER BY id_observation DESC");
            $stmt->execute();

            $observation_list = array();
            while($data = $stmt->fetch()) {
                $observation_info = new stdClass();
                $observation_info->id_observation = $data['id_observation'];
                $observation_info->nom_observation = $data['nom_observation'];
                $observation_info->description = $data['description'];
                $observation_info->type_observation = $data['type_observation'];
                
                $observation_list[] = $observation_info;
            }

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "observation_list" => $observation_list
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "observation_list" => array()
            );
        }
    }

    // Log call
    function log_call($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "success" => 0
            );
        }

        // Extract request data
        $code_barre = isset($request_data['code_barre']) ? $request_data['code_barre'] : '';
        $id_runsheet = isset($request_data['id_runsheet']) ? $request_data['id_runsheet'] : '';
        $expediteur = isset($request_data['expediteur']) ? $request_data['expediteur'] : '';
        $file_name = isset($request_data['file_name']) ? $request_data['file_name'] : '';

        if(empty($code_barre)){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Missing code_barre"),
                "success" => 0
            );
        }

        try {
            $date_time = date('Y-m-d H:i:s');

            // Insert call log
            $stmt = $this->conn->prepare("INSERT INTO log_call (code_barre, id_runsheet, expediteur, file_name, date_call, id_livreur) VALUES (:code_barre, :id_runsheet, :expediteur, :file_name, :date_time, :livreur_id)");
            
            $stmt->bindParam(':code_barre', $code_barre);
            $stmt->bindParam(':id_runsheet', $id_runsheet);
            $stmt->bindParam(':expediteur', $expediteur);
            $stmt->bindParam(':file_name', $file_name);
            $stmt->bindParam(':date_time', $date_time);
            $stmt->bindParam(':livreur_id', $livreur_id);
            
            $stmt->execute();

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "success" => 1,
                "message" => "Call logged successfully"
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "success" => 0
            );
        }
    }

    // Add cheque
    function add_cheque($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "success" => 0
            );
        }

        // Extract request data
        $code_barre = isset($request_data['code_barre']) ? $request_data['code_barre'] : '';
        $num_cheque = isset($request_data['num_cheque']) ? $request_data['num_cheque'] : '';
        $montant_cheque = isset($request_data['montant_cheque']) ? $request_data['montant_cheque'] : '';
        $banque = isset($request_data['banque']) ? $request_data['banque'] : '';

        if(empty($code_barre) || empty($num_cheque) || empty($montant_cheque)){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Missing required fields: code_barre, num_cheque, montant_cheque"),
                "success" => 0
            );
        }

        try {
            $date_time = date('Y-m-d H:i:s');

            // Insert cheque
            $stmt = $this->conn->prepare("INSERT INTO cheque (code_barre, num_cheque, montant_cheque, banque, date_add, id_livreur) VALUES (:code_barre, :num_cheque, :montant_cheque, :banque, :date_time, :livreur_id)");
            
            $stmt->bindParam(':code_barre', $code_barre);
            $stmt->bindParam(':num_cheque', $num_cheque);
            $stmt->bindParam(':montant_cheque', $montant_cheque);
            $stmt->bindParam(':banque', $banque);
            $stmt->bindParam(':date_time', $date_time);
            $stmt->bindParam(':livreur_id', $livreur_id);
            
            $stmt->execute();

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "success" => 1,
                "message" => "Cheque added successfully"
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "success" => 0
            );
        }
    }

    // Update type commande
    function update_type_commande($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "success" => 0
            );
        }

        // Extract request data
        $code_barre = isset($request_data['code_barre']) ? $request_data['code_barre'] : '';
        $type_commande = isset($request_data['type_commande']) ? $request_data['type_commande'] : '';

        if(empty($code_barre) || empty($type_commande)){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Missing required fields: code_barre, type_commande"),
                "success" => 0
            );
        }

        try {
            // Update commande type
            $stmt = $this->conn->prepare("UPDATE commande SET type_commande=:type_commande WHERE code_barres_cmd=:code_barre");
            $stmt->bindParam(':type_commande', $type_commande);
            $stmt->bindParam(':code_barre', $code_barre);
            $stmt->execute();

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "success" => 1,
                "message" => "Type commande updated successfully"
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "success" => 0
            );
        }
    }
}
?>
