<?php
include_once 'authentication.php';

class Runsheet{

    // database connection and table name
    private $conn;
    private $auth;

    // constructor with $db as database connection
    public function __construct($db){
        $this->conn = $db;
        $this->auth = new Authentication($db);
    }

    // List runsheets for livreur
    function list_runsheet($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "runsheet_list" => array()
            );
        }

        try {
            // Get runsheet data
            $stmt = $this->conn->prepare("select * from runsheet where id_liv_runsheet=:livreur and etat_runsheet=0 and nbr_cmd_runsheet <> 0 order by id_runsheet desc");
            $stmt->bindParam(':livreur', $livreur_id);
            $stmt->execute();

            $runsheet_list = array();
            while($data = $stmt->fetch()) {
                // Count total parcels
                $stmt_total_colis = $this->conn->prepare("SELECT COUNT(id_log) as total_colis FROM runsheet_log WHERE id_run=:id_runsheet");
                $stmt_total_colis->bindParam(':id_runsheet', $data['id_runsheet']);
                $stmt_total_colis->execute();
                $data_total_colis = $stmt_total_colis->fetch();
                
                // Count delivered parcels
                $stmt_total_colis_livre = $this->conn->prepare("SELECT COUNT(id_log) as total_colis FROM runsheet_log WHERE id_run=:id_runsheet and etat_cmd_liv=2");
                $stmt_total_colis_livre->bindParam(':id_runsheet', $data['id_runsheet']);
                $stmt_total_colis_livre->execute();
                $data_total_colis_livre = $stmt_total_colis_livre->fetch();

                $runsheet_info = new stdClass();
                $runsheet_info->id = $data['id_runsheet'];
                $runsheet_info->date = $data['date_runsheet'];
                $runsheet_info->colis = $data_total_colis['total_colis'];
                $runsheet_info->colis_livre = $data_total_colis_livre['total_colis'];
                
                $runsheet_list[] = $runsheet_info;
            }

            $count = $stmt->rowCount();
            if($count < 1){
                return array(
                    "HasErrors" => 1,
                    "ErrorsTxt" => array("No active runsheets found"),
                    "runsheet_list" => array()
                );
            }

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "runsheet_list" => $runsheet_list
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "runsheet_list" => array()
            );
        }
    }

    // List parcels in a runsheet
    function list_parcel_runsheet($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "parcel_list" => array()
            );
        }

        $id_runsheet = isset($request_data['id_runsheet']) ? $request_data['id_runsheet'] : '';
        
        if(empty($id_runsheet)){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Missing id_runsheet"),
                "parcel_list" => array()
            );
        }

        try {
            // Get parcels in runsheet
            $stmt = $this->conn->prepare("SELECT rl.*, c.* FROM runsheet_log rl JOIN commande c ON rl.code_barres_cmd = c.code_barres_cmd WHERE rl.id_run=:id_runsheet ORDER BY rl.id_log DESC");
            $stmt->bindParam(':id_runsheet', $id_runsheet);
            $stmt->execute();

            $parcel_list = array();
            while($data = $stmt->fetch()) {
                $parcel_info = new stdClass();
                $parcel_info->id = $data['code_barres_cmd'];
                $parcel_info->date = $data['date_cmd'];
                $parcel_info->nom_cli = $data['nom_cli'];
                $parcel_info->adr_cli = $data['adr_cli'];
                $parcel_info->ville_cli = $data['ville_cli'];
                $parcel_info->tel_cli = $data['tel_cli'];
                $parcel_info->ttc_cmd = $data['ttc_cmd'];
                $parcel_info->etat_cmd_liv = $data['etat_cmd_liv'];
                $parcel_info->observ_cmd = $data['observ_cmd'];
                $parcel_info->contenu_cmd = $data['contenu_cmd'];
                $parcel_info->nbr_colis = $data['nbr_colis'];
                
                $parcel_list[] = $parcel_info;
            }

            $count = $stmt->rowCount();
            if($count < 1){
                return array(
                    "HasErrors" => 1,
                    "ErrorsTxt" => array("No parcels found in this runsheet"),
                    "parcel_list" => array()
                );
            }

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "parcel_list" => $parcel_list
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "parcel_list" => array()
            );
        }
    }

    // Add new runsheet
    function add_runsheet($request_data){
        $error_msg = array();

        // Validate authentication
        $livreur_id = $this->auth->validate_request_auth($request_data);
        if(!$livreur_id){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Failed authentication"),
                "success" => 0
            );
        }

        // Extract request data
        $matricule = isset($request_data['matricule']) ? $request_data['matricule'] : '';
        $date_runsheet = isset($request_data['date_runsheet']) ? $request_data['date_runsheet'] : date('Y-m-d');
        $observation = isset($request_data['observation']) ? $request_data['observation'] : '';
        $parcels = isset($request_data['parcels']) ? $request_data['parcels'] : array();

        if(empty($matricule)){
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Missing matricule"),
                "success" => 0
            );
        }

        try {
            $agence_id = $this->auth->get_agence_livreur($livreur_id);
            $date_creation = date('Y-m-d H:i:s');
            $nbr_cmd = count($parcels);

            // Insert new runsheet
            $stmt = $this->conn->prepare("INSERT INTO runsheet (matricule, date_runsheet, id_liv_runsheet, id_agence, nbr_cmd_runsheet, observation, etat_runsheet, date_creation) VALUES (:matricule, :date_runsheet, :livreur_id, :agence_id, :nbr_cmd, :observation, 0, :date_creation)");
            
            $stmt->bindParam(':matricule', $matricule);
            $stmt->bindParam(':date_runsheet', $date_runsheet);
            $stmt->bindParam(':livreur_id', $livreur_id);
            $stmt->bindParam(':agence_id', $agence_id);
            $stmt->bindParam(':nbr_cmd', $nbr_cmd);
            $stmt->bindParam(':observation', $observation);
            $stmt->bindParam(':date_creation', $date_creation);
            
            $stmt->execute();
            $runsheet_id = $this->conn->lastInsertId();

            // Add parcels to runsheet
            foreach($parcels as $parcel_code) {
                $stmt_parcel = $this->conn->prepare("INSERT INTO runsheet_log (id_run, code_barres_cmd, date_add, etat_cmd_liv) VALUES (:runsheet_id, :parcel_code, :date_creation, 1)");
                $stmt_parcel->bindParam(':runsheet_id', $runsheet_id);
                $stmt_parcel->bindParam(':parcel_code', $parcel_code);
                $stmt_parcel->bindParam(':date_creation', $date_creation);
                $stmt_parcel->execute();
            }

            return array(
                "HasErrors" => 0,
                "ErrorsTxt" => array(),
                "success" => 1,
                "runsheet_id" => $runsheet_id,
                "message" => "Runsheet created successfully"
            );

        } catch(Exception $e) {
            return array(
                "HasErrors" => 1,
                "ErrorsTxt" => array("Database error: " . $e->getMessage()),
                "success" => 0
            );
        }
    }
}
?>
