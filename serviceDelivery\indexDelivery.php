<?php
########################################################
########################################################
#############Declaration de l'objet Pickup##############
########################################################
########################################################
class Pickup{
    public $code_barre;
    public $tracking_number;
    public $frs;
    public $id_frs;
    public $agence;
    public $date_add;
    public $date_pick;
    public $prix;
    public $nom;
    public $gouvernerat;
    public $ville;
    public $adresse;
    public $tel;
    public $tel2;
    public $designation;
    public $nb_article;
    public $msg;
    public $etat;
    public $paye;
    public $date_stat;
    public $agence_dest;
    public $transmit;
    public $recu;
    public $id_recette;
    public $unlink;
    public $modif;
    public $id_runsheet;
    public $login;
    public $pwd;
    public $longitude;
    public $latitude;
    public $motif;
    public $magasin;
    public $id_pickup;
    public $date_report;
    public $montant_espece;
    public $type_payment;
    public $list_mnt_cheque;
    public $list_num_cheque;
    public $nbr_colis_recu;
}
class Livreur{
    public $login;
    public $pwd;
    public $runsheet;
    public $id_pickup;
    public $etat_pickup;
    public $nbr_colis;
}
class Call{
    public $login;
    public $pwd;
    public $code_barre;
    public $id_runsheet;
    public $expediteur;
    public $file_name;
}
error_reporting(E_ALL);
ini_set('error_reporting', E_ALL);
ini_set("display_errors", "1");


###########################################################
###########################################################
################API ListParcelRunsheet#####################
##########Renvoi la liste des colis d'un Runsheet##########
###########################################################
###########################################################
$livreur = new Livreur();
$livreur->login = "rachwen";
$livreur->pwd = "rachwen";
$livreur->runsheet = "336";
// initialize SOAP client and call web service function ListParcelRunsheet
$client_4=new SoapClient('https://adex.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_4 = $client_4->ListParcelRunsheet($livreur);
echo "<pre>ListParcelRunsheet<br>";
// dump response
print_r($resp_4);
echo "</pre>";
die();

###########################################################
###########################################################
################API ListRunsheet###########################
##########Renvoi la liste des Runsheet#####################
###########################################################
###########################################################
$livreur = new Livreur();
$livreur->login = "rachwen";
$livreur->pwd = "rachwen";
// initialize SOAP client and call web service function ListRunsheet
$client_4=new SoapClient('https://adex.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_4 = $client_4->ListRunsheet($livreur);
echo "<pre>ListRunsheet<br>";
// dump response
print_r($resp_4);
echo "</pre>";
die();



###########################################################
###########################################################
################API GetObservation#################################
##########Renvoi la liste des observations##########################
###########################################################
###########################################################
$livreur = new Livreur();
$livreur->login = "rachwen";
$livreur->pwd = "123";
// initialize SOAP client and call web service function pickup
$client_4=new SoapClient('https://livraison.magitech.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_4 = $client_4->GetObservation($livreur);
echo "<pre>GetObservation<br>";
// dump response
print_r($resp_4);
echo "</pre>";
die();



###########################################################
###########################################################
################API ListRunsheet###########################
##########Renvoi la liste des Runsheet#####################
###########################################################
###########################################################
$livreur = new Livreur();
$livreur->login = "nizar";
$livreur->pwd = "123";
// initialize SOAP client and call web service function ListRunsheet
$client_4=new SoapClient('https://livraison.magitech.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_4 = $client_4->ListRunsheet($livreur);
echo "<pre>ListRunsheet<br>";
// dump response
print_r($resp_4);
echo "</pre>";
die();




###########################################################
###########################################################
################API GetSms#################################
##########Renvoi la liste des sms##########################
###########################################################
###########################################################
$livreur = new Livreur();
$livreur->login = "nizar";
$livreur->pwd = "123";
// initialize SOAP client and call web service function pickup
$client_4=new SoapClient('https://livraison.magitech.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_4 = $client_4->GetSms($livreur);
echo "<pre>GetSms<br>";
// dump response
print_r($resp_4);
echo "</pre>";
die();


########################################################################
########################################################################
##########################API TrackShipment#############################
##########Renvoi la liste de tous les etats de la commande##############
########################################################################
########################################################################
$pickup_3 = new Pickup();
$pickup_3->login = "nizar";
$pickup_3->pwd = "123";
$pickup_3->tracking_number = "2110050700008";
// initialize SOAP client and call web service function TrackShipment
$client_3=new SoapClient('https://livraison.magitech.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_3 = $client_3->TrackShipment($pickup_3);
echo "<pre>TrackShipment<br>";
print_r($resp_3);
echo " </pre>";

die();




########################################################################
########################################################################
##########################API InfoParcel################################
##########Renvoi les infos de la commande###############################
########################################################################
########################################################################
$pickup_3 = new Pickup();
$pickup_3->login = "nizar";
$pickup_3->pwd = "123";
$pickup_3->tracking_number = "2109270700095";
// initialize SOAP client and call web service function TrackShipment
$client_3=new SoapClient('https://livraison.magitech.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_3 = $client_3->InfoParcel($pickup_3);
echo "<pre>InfoParcel<br>";
print_r($resp_3);
echo " </pre>";

die();






###########################################################
###########################################################
################API ListPickUp#############################
##########Renvoi la liste des pickup#######################
###########################################################
###########################################################
$livreur = new Livreur();
$livreur->login = "nizar";
$livreur->pwd = "123";
// initialize SOAP client and call web service function pickup
$client_4=new SoapClient('https://livraison.magitech.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_4 = $client_4->ListPickUp($livreur);
echo "<pre>ListPickUp<br>";
// dump response
print_r($resp_4);
echo "</pre>";
die();












###########################################################
###########################################################
################API UpdatePickupStatus#####################
##########Renvoi la liste des pickup#######################
###########################################################
###########################################################
$livreur = new Livreur();
$livreur->login = "nizar";
$livreur->pwd = "123";
$livreur->id_pickup = "3";
$livreur->etat_pickup = "0";
// initialize SOAP client and call web service function UpdatePickupStatus
$client_4=new SoapClient('https://livraison.magitech.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_4 = $client_4->UpdatePickupStatus($livreur);
echo "<pre>UpdatePickupStatus<br>";
// dump response
print_r($resp_4);
echo "</pre>";
die();








########################################################################
########################################################################
##########################API Authentication############################
##########Authentication################################################
########################################################################
########################################################################
$pickup_3 = new Pickup();
$pickup_3->login = "nizar";
$pickup_3->pwd = "123";
// initialize SOAP client and call web service function Authentication
$client_3=new SoapClient('https://livraison.magitech.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_3 = $client_3->Authentication($pickup_3);
echo "<pre>Authentication<br>";
print_r($resp_3);
echo " </pre>";

die();













###########################################################
###########################################################
################API UpdateShipmentStatus###################
##########Modifi l'etat actuel de la commande##############
###########################################################
###########################################################
$pickup_4 = new Pickup();
$pickup_4->login = "oussema";
$pickup_4->pwd = "123";
$pickup_4->tracking_number = "889295753101";
$pickup_4->id_runsheet = "11755";
$pickup_4->etat = "2";//0=>'En attente',1=>'En cours',2=>'Livrée',11=>'RTN dépot', 7=>'Retour Client / Agence',8=>'Au dépôt',10=>'Livrées payées',30=>'Retour réçu',32=>'Retour payé',31=>'Retour définitif',5=>'Retour Expéditeur'
// initialize SOAP client and call web service function UpdateShipmentStatus
$client_4=new SoapClient('https://livraison.magitech.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_4 = $client_4->UpdateShipmentStatus($pickup_4);
echo "<br>UpdateShipmentStatus<br>";
// dump response
print_r($resp_4);
die();
###########################################################
###########################################################
################API LogCall###############################
##########Enregistre le log d'appel########################
###########################################################
###########################################################
$call = new Call();
$call->login = "oussema";
$call->pwd = "123";
$call->code_barre = "193503645182";
$call->id_runsheet = "11730";
$call->expediteur = "0";
$call->file_name = "582654165415_2021_06_22.amr";
// initialize SOAP client and call web service function ListParcelRunsheet
$client_4=new SoapClient('https://livraison.magitech.tn/api/serviceDelivery.php?wsdl',['trace'=>1,'cache_wsdl'=>WSDL_CACHE_NONE]);
$resp_4 = $client_4->LogCall($call);
echo "<pre>LogCall<br>";
// dump response
print_r($resp_4);
echo "</pre>";
die();
