<?php
// turn off WSDL caching
ini_set("soap.wsdl_cache_enabled","0");

//MySQL connection details.
$host = '**************:3306';
$user = 'admin_livraison';
$pass = 'L8d86p?b0';
$database = 'admin_livraison';

//Custom PDO options.
$options = array(
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_EMULATE_PREPARES => false
);

//Connect to MySQL and instantiate our PDO object.
$conn = new PDO("mysql:host=$host;dbname=$database", $user, $pass, $options);
$conn->exec('SET NAMES utf8');