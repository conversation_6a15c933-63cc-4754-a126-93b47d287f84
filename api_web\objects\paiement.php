<?php
include_once '../objects/authentification.php';

class paiement{

    // database connection and table name
    private $conn;

    // constructor with $db as database connection
    public function __construct($db){
        $this->authentification =  new authentification($db);
         $verif_auth=$this->authentification->authentification_user();
         if($verif_auth==0){
             exit;
        }

        $this->conn = $db;

    }


// get the details of paiement
    function get_paiement(){
        $this->id_exp=$this->authentification->id_exp;
        $this->liste_paie=array();
        $this->liste_cmd=array();
        $query = "select * from paiement where id > ?  and etat=2 and id_exped=?";


        // prepare query statement
        $stmt = $this->conn->prepare( $query );

        // bind id of product to be updated
        $stmt->bindParam(1, $this->num_der_paie);
        $stmt->bindParam(2, $this->id_exp);

        // execute query
        $stmt->execute();
        $foundRows = false;
        // get retrieved row
        while($row = $stmt->fetch(PDO::FETCH_ASSOC)){
            $foundRows = true;
            // set values to object properties
            $this->id = $row['id'];
            $this->montant_esp = $row['montant_esp'];
            $this->montant_cheque = $row['montant_cheque'];
            $this->montant_cmd_tot = $row['montant_cmd_tot'];
            $this->nb_cheque = $row['nb_cheque'];
            $this->date_cloture = $row['date_cloture'];
            $this->num = $row['num'];
            $this->espece_valide = $row['espece_valide'];
            $this->cheque_valide = $row['cheque_valide'];
            $this->nb_colis_ret_tot = $row['nb_colis_ret_tot'];
            $this->nb_colis_liv_tot = $row['nb_colis_liv_tot'];
            $this->mnt_colis_liv_tot = $row['mnt_colis_liv_tot'];
            $this->mnt_colis_ret_tot = $row['mnt_colis_ret_tot'];
            $this->mnt_facture = $row['mnt_facture']+$row['timbre'];
            $this->id_facture = $row['id_facture'];
            $this->mt_pick = $row['mt_pick'];
            $this->nb_pick = $row['nb_pick'];
            $this->date_paye = $row['date_paye'];
            $FacturePdf='';
            if($this->id_facture!=0){
                $FacturePdf='https://backoffice.adex.tn/composants/mc_paiement/facturePdf.php?id='.$this->id;
            }


            $queryFact = "SELECT * FROM `facture` WHERE id= ?";
            $stmtFact = $this->conn->prepare( $queryFact );
            $stmtFact->bindParam(1, $this->id_facture);
            $stmtFact->execute();
            $rowFact = $stmtFact->fetch(PDO::FETCH_ASSOC);
            $num_facture=$rowFact['num'];

            //array reglement
                    $query1 = "select * from commande where id_paiement =?";
                    $stmt1 = $this->conn->prepare( $query1 );
                    $stmt1->bindParam(1, $this->id);
                    $stmt1->execute();
                     while($row1 = $stmt1->fetch(PDO::FETCH_ASSOC)){

                            $id_cmd=$row1['id_cmd'];
                            $code_barres_cmd=$row1['code_barres_cmd'];
                            $code_barres_ext=$row1['code_barres_ext'];
                            $nom_cli=$row1['nom_cli'];
                            $ttc_cmd=$row1['ttc_cmd'];
                            $date_livraison_retour='' ;
                            if($row1['date_livraison_retour']!=''){
                                $date_livraison_retour=date('Y-m-d',$row1['date_livraison_retour']);
                            }
                            $date_enlevement='' ;
                            if($row1['date_reception_cmd']!=''){
                                $date_enlevement=date('Y-m-d',$row1['date_reception_cmd']);
                            }

                            $etat_cmd=$row1['etat_cmd'];
                            $frais_livraison=$row1['frais_livraison'];
                            if($etat_cmd=='5'){
                                $txtEtat='Livré';
                            }
                            if($etat_cmd=='6'){
                                $txtEtat='Retour';
                            }

                            if($row1['tel_cli2']!=''){
                                $tel2= ' / '.$row1['tel_cli2'];
                            }else{$tel2='';}
                            $telCli=$row1['tel_cli'].$tel2;

                            $espece='0';
                            $cheque='0';
                            $query2 = "SELECT * FROM `reglement_client_ligne` WHERE  id_cmd =?";
                            $stmt2 = $this->conn->prepare( $query2 );
                            $stmt2->bindParam(1, $id_cmd);
                            $stmt2->execute();
                            $row2 = $stmt2->fetch(PDO::FETCH_ASSOC);
                            $espece=$row2['mnt_epece'];
                            $cheque=$row2['mnt_cheque'];
                            $type_reg=$row2['type_reg'];

                            $num_cheque='';
                            $mnt_cheque_detail='';
                            $query3 = "SELECT * FROM `reglement_client_cheque` WHERE id_reg in (select id_reg from `reglement_client_ligne` where id_cmd =?)";
                            $stmt3 = $this->conn->prepare( $query3 );
                            $stmt3->bindParam(1, $id_cmd);
                            $stmt3->execute();
                             while($row3 = $stmt3->fetch(PDO::FETCH_ASSOC)){
                                  $mnt_cheque_detail.=$row3['mnt_cheque']."/";
                                  $num_cheque.=$row3['num_cheque']."/";
                             }


                            array_push($this->liste_cmd,array(
                                    "code_barres_cmd" => $code_barres_cmd,
                                    "code_barres_ext" => $code_barres_ext,
                                    "nom_cli" => $nom_cli,
                                    "date_livraison_retour" => $date_livraison_retour,
                                    "date_enlevement" => $date_enlevement,
                                    "etat_cmd" => $etat_cmd,
                                    "txtEtat" => $txtEtat,
                                    "telCli" => $telCli,
                                    "frais_livraison" => $frais_livraison,
                                    "contre_remb" => $ttc_cmd,
                                    "type_reg" => $type_reg,
                                    "espece" => $espece,
                                    "cheque_total" => $cheque,
                                    "mnt_cheque_detail" => $mnt_cheque_detail,
                                    "num_cheque" => $num_cheque
                                    ));
                     }
                   //array paiement
                 array_push($this->liste_paie,array(
                            "id" => $this->id,
                            "num" => $this->num,
                            "montant_esp" => $this->montant_esp,
                            "montant_cheque" => $this->montant_cheque,
                            "nb_cheque" => $this->nb_cheque,
                            "nb_colis_ret_tot" => $this->nb_colis_ret_tot,
                            "nb_colis_liv_tot" => $this->nb_colis_liv_tot,
                            "mnt_colis_liv_tot" => $this->mnt_colis_liv_tot,
                            "mnt_colis_ret_tot" => $this->mnt_colis_ret_tot,
                            "motif" => $this->nb_cheque,
                            "date_cloture" => $this->date_cloture,
                            "date_paye" => $this->date_paye,
                            "mt_pick" => $this->mt_pick,
                            "nb_pick" => $this->nb_pick,
                            "mnt_facture" => $this->mnt_facture,
                            "num_facture" => $num_facture,
                            "detailsColisPdf" => 'https://backoffice.adex.tn/composants/mc_paiement/detailsColisPdf.php?id='.$this->id,
                            "detailsPaiePdf" => 'https://backoffice.adex.tn/composants/mc_paiement/detailsPaiePdf.php?id='.$this->id,
                            "FacturePdf" => $FacturePdf,
                            "colis" => $this->liste_cmd

                            ));
            $this->liste_cmd=array();
        }
//        if(count($row)>0){
//            return true;
//        }
        if ($foundRows) {
            return true;
        }
        return false;
    }
}

