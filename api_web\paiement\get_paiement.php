<?php

//error_reporting(E_ALL);
//ini_set('error_reporting', E_ALL);
//ini_set("display_errors", "1");
$entityBody = json_decode(file_get_contents('php://input'),true);
//

header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// include database and object files
include_once '../config/database.php';
include_once '../objects/paiement.php';
 //get database connection
$database = new Database();
$db = $database->getConnection();
$paiement = new paiement($db);
$paiement->num_der_paie=$entityBody['num_der_paie'];
if($paiement->get_paiement()){
    echo json_encode($paiement->liste_paie);

}else{
    echo json_encode(array("message" => "ERROR.","success"=>0));
}

?>