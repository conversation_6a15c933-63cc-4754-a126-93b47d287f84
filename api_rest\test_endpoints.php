<?php
/**
 * Test file for REST API endpoints
 * This file demonstrates how to call the REST API endpoints
 */

// Test credentials (replace with actual test credentials)
$test_credentials = array(
    'login' => 'test_user',
    'pwd' => 'test_password'
);

// Base URL for the API
$base_url = 'http://localhost/api_rest/';

/**
 * Make a POST request to an endpoint
 */
function make_request($endpoint, $data) {
    global $base_url;
    
    $url = $base_url . $endpoint;
    $json_data = json_encode($data);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
        'Content-Length: ' . strlen($json_data)
    ));
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return array(
        'http_code' => $http_code,
        'response' => json_decode($response, true)
    );
}

echo "<h1>REST API Endpoint Tests</h1>";

// Test 1: Authentication
echo "<h2>1. Testing Authentication</h2>";
$auth_data = $test_credentials;
$result = make_request('authentication/authenticate.php', $auth_data);
echo "<pre>";
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT);
echo "</pre>";

// Test 2: List Runsheet
echo "<h2>2. Testing List Runsheet</h2>";
$runsheet_data = $test_credentials;
$result = make_request('runsheet/list_runsheet.php', $runsheet_data);
echo "<pre>";
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT);
echo "</pre>";

// Test 3: List Pickup
echo "<h2>3. Testing List Pickup</h2>";
$pickup_data = $test_credentials;
$result = make_request('pickup/list_pickup.php', $pickup_data);
echo "<pre>";
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT);
echo "</pre>";

// Test 4: Get SMS
echo "<h2>4. Testing Get SMS</h2>";
$sms_data = $test_credentials;
$result = make_request('utils/get_sms.php', $sms_data);
echo "<pre>";
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT);
echo "</pre>";

// Test 5: Get Observations
echo "<h2>5. Testing Get Observations</h2>";
$obs_data = $test_credentials;
$result = make_request('utils/get_observation.php', $obs_data);
echo "<pre>";
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT);
echo "</pre>";

// Test 6: Track Shipment (with sample tracking number)
echo "<h2>6. Testing Track Shipment</h2>";
$track_data = array_merge($test_credentials, array(
    'tracking_number' => '2110050700008'
));
$result = make_request('delivery/track_shipment.php', $track_data);
echo "<pre>";
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT);
echo "</pre>";

// Test 7: Info Parcel (with sample tracking number)
echo "<h2>7. Testing Info Parcel</h2>";
$parcel_data = array_merge($test_credentials, array(
    'tracking_number' => '2110050700008'
));
$result = make_request('delivery/info_parcel.php', $parcel_data);
echo "<pre>";
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . json_encode($result['response'], JSON_PRETTY_PRINT);
echo "</pre>";

echo "<h2>Test Summary</h2>";
echo "<p>All endpoints have been tested. Check the responses above to verify functionality.</p>";
echo "<p><strong>Note:</strong> Update the test credentials and tracking numbers with actual values from your database.</p>";
?>
