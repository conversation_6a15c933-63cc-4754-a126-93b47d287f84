<?php
// Get JSON input
$entityBody = json_decode(file_get_contents('php://input'), true);

// Required headers
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Include database and object files
include_once '../config/database.php';
include_once '../objects/authentication.php';

// Get database connection
$database = new Database();
$db = $database->getConnection();

// Initialize authentication object
$auth = new Authentication($db);

// Validate input
if(!isset($entityBody['login']) || !isset($entityBody['pwd'])){
    echo json_encode(array(
        "HasErrors" => 1,
        "ErrorsTxt" => array("Missing login or password"),
        "success" => 0
    ));
    exit;
}

// Get authentication info
$result = $auth->get_authentication_info($entityBody['login'], $entityBody['pwd']);

// Return response
if($result['HasErrors'] == 0){
    echo json_encode(array(
        "HasErrors" => 0,
        "ErrorsTxt" => array(),
        "success" => 1,
        "livreur_info" => $result['livreur_info'],
        "zones" => $result['zones']
    ));
} else {
    echo json_encode(array(
        "HasErrors" => 1,
        "ErrorsTxt" => $result['ErrorsTxt'],
        "success" => 0,
        "livreur_info" => null,
        "zones" => array()
    ));
}
?>
